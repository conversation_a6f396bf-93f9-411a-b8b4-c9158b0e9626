[{"merged": "com.baseflow.geocoding.geocoding_android-release-25:/layout-v21/notification_action_tombstone.xml", "source": "com.baseflow.geocoding.geocoding_android-core-1.13.1-15:/layout-v21/notification_action_tombstone.xml"}, {"merged": "com.baseflow.geocoding.geocoding_android-release-25:/layout-v21/notification_template_icon_group.xml", "source": "com.baseflow.geocoding.geocoding_android-core-1.13.1-15:/layout-v21/notification_template_icon_group.xml"}, {"merged": "com.baseflow.geocoding.geocoding_android-release-25:/layout-v21/notification_action.xml", "source": "com.baseflow.geocoding.geocoding_android-core-1.13.1-15:/layout-v21/notification_action.xml"}, {"merged": "com.baseflow.geocoding.geocoding_android-release-25:/layout-v21/notification_template_custom_big.xml", "source": "com.baseflow.geocoding.geocoding_android-core-1.13.1-15:/layout-v21/notification_template_custom_big.xml"}]