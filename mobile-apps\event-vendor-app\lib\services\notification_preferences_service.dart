import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/notification_preferences_model.dart';
import '../config/firebase_config.dart';

class NotificationPreferencesService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get notification preferences for a vendor
  Future<NotificationPreferences> getNotificationPreferences(String vendorId) async {
    try {
      final doc = await _firestore
          .collection(FirebaseConfig.eventVendorsCollection)
          .doc(vendorId)
          .collection('preferences')
          .doc('notifications')
          .get();

      if (doc.exists) {
        return NotificationPreferences.fromFirestore(doc);
      } else {
        // Return default preferences if none exist
        return NotificationPreferences.defaultSettings();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting notification preferences: $e');
      }
      return NotificationPreferences.defaultSettings();
    }
  }

  // Update notification preferences
  Future<bool> updateNotificationPreferences(
    String vendorId,
    NotificationPreferences preferences,
  ) async {
    try {
      await _firestore
          .collection(FirebaseConfig.eventVendorsCollection)
          .doc(vendorId)
          .collection('preferences')
          .doc('notifications')
          .set(preferences.toFirestore(), SetOptions(merge: true));

      if (kDebugMode) {
        print('✅ Notification preferences updated for vendor: $vendorId');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating notification preferences: $e');
      }
      return false;
    }
  }

  // Stream notification preferences for real-time updates
  Stream<NotificationPreferences> streamNotificationPreferences(String vendorId) {
    return _firestore
        .collection(FirebaseConfig.eventVendorsCollection)
        .doc(vendorId)
        .collection('preferences')
        .doc('notifications')
        .snapshots()
        .map((doc) {
      if (doc.exists) {
        return NotificationPreferences.fromFirestore(doc);
      } else {
        return NotificationPreferences.defaultSettings();
      }
    });
  }

  // Update specific notification setting
  Future<bool> updateNotificationSetting(
    String vendorId,
    String settingKey,
    bool value,
  ) async {
    try {
      await _firestore
          .collection(FirebaseConfig.eventVendorsCollection)
          .doc(vendorId)
          .collection('preferences')
          .doc('notifications')
          .set({
        settingKey: value,
        'updatedAt': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));

      if (kDebugMode) {
        print('✅ Notification setting $settingKey updated to $value for vendor: $vendorId');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating notification setting: $e');
      }
      return false;
    }
  }

  // Reset to default preferences
  Future<bool> resetToDefaults(String vendorId) async {
    try {
      final defaultPrefs = NotificationPreferences.defaultSettings();
      return await updateNotificationPreferences(vendorId, defaultPrefs);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error resetting notification preferences: $e');
      }
      return false;
    }
  }
}
