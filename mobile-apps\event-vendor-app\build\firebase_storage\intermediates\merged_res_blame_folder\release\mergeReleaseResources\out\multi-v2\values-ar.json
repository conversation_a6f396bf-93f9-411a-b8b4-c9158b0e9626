{"logs": [{"outputFile": "io.flutter.plugins.firebase.storage.firebase_storage-release-29:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\d51cd53175e633a38dc9073f471970fc\\transformed\\core-1.13.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "2,3,4,5,6,7,8,27", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,2863", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,2959"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\e635047f1d7f095c018a5f117412df59\\transformed\\jetified-play-services-base-18.1.0\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,433,551,652,786,910,1017,1115,1248,1348,1494,1612,1747,1889,1949,2011", "endColumns": "99,139,117,100,133,123,106,97,132,99,145,117,134,141,59,61,79", "endOffsets": "292,432,550,651,785,909,1016,1114,1247,1347,1493,1611,1746,1888,1948,2010,2090"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "767,871,1015,1137,1242,1380,1508,1619,1851,1988,2092,2242,2364,2503,2649,2713,2779", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "866,1010,1132,1237,1375,1503,1614,1716,1983,2087,2237,2359,2498,2644,2708,2774,2858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\48c8606bfcca6d333ba9734a73c1c09c\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-ar\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1721", "endColumns": "129", "endOffsets": "1846"}}]}, {"outputFile": "io.flutter.plugins.firebase.storage.firebase_storage-mergeReleaseResources-27:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\d51cd53175e633a38dc9073f471970fc\\transformed\\core-1.13.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "2,3,4,5,6,7,8,27", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,2863", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,2959"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\e635047f1d7f095c018a5f117412df59\\transformed\\jetified-play-services-base-18.1.0\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,433,551,652,786,910,1017,1115,1248,1348,1494,1612,1747,1889,1949,2011", "endColumns": "99,139,117,100,133,123,106,97,132,99,145,117,134,141,59,61,79", "endOffsets": "292,432,550,651,785,909,1016,1114,1247,1347,1493,1611,1746,1888,1948,2010,2090"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "767,871,1015,1137,1242,1380,1508,1619,1851,1988,2092,2242,2364,2503,2649,2713,2779", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "866,1010,1132,1237,1375,1503,1614,1716,1983,2087,2237,2359,2498,2644,2708,2774,2858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\48c8606bfcca6d333ba9734a73c1c09c\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-ar\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1721", "endColumns": "129", "endOffsets": "1846"}}]}]}