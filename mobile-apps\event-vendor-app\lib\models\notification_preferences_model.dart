import 'package:cloud_firestore/cloud_firestore.dart';

class NotificationPreferences {
  final bool pushNotifications;
  final bool emailNotifications;
  final bool smsNotifications;
  final bool bookingAlerts;
  final bool paymentAlerts;
  final bool teamUpdates;
  final bool marketingEmails;
  final bool systemUpdates;
  final DateTime updatedAt;

  const NotificationPreferences({
    required this.pushNotifications,
    required this.emailNotifications,
    required this.smsNotifications,
    required this.bookingAlerts,
    required this.paymentAlerts,
    required this.teamUpdates,
    required this.marketingEmails,
    required this.systemUpdates,
    required this.updatedAt,
  });

  // Default preferences
  factory NotificationPreferences.defaultSettings() {
    return NotificationPreferences(
      pushNotifications: true,
      emailNotifications: true,
      smsNotifications: false,
      bookingAlerts: true,
      paymentAlerts: true,
      teamUpdates: true,
      marketingEmails: false,
      systemUpdates: true,
      updatedAt: DateTime.now(),
    );
  }

  // From Firestore
  factory NotificationPreferences.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return NotificationPreferences(
      pushNotifications: data['pushNotifications'] ?? true,
      emailNotifications: data['emailNotifications'] ?? true,
      smsNotifications: data['smsNotifications'] ?? false,
      bookingAlerts: data['bookingAlerts'] ?? true,
      paymentAlerts: data['paymentAlerts'] ?? true,
      teamUpdates: data['teamUpdates'] ?? true,
      marketingEmails: data['marketingEmails'] ?? false,
      systemUpdates: data['systemUpdates'] ?? true,
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  // To Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'pushNotifications': pushNotifications,
      'emailNotifications': emailNotifications,
      'smsNotifications': smsNotifications,
      'bookingAlerts': bookingAlerts,
      'paymentAlerts': paymentAlerts,
      'teamUpdates': teamUpdates,
      'marketingEmails': marketingEmails,
      'systemUpdates': systemUpdates,
      'updatedAt': FieldValue.serverTimestamp(),
    };
  }

  // Copy with
  NotificationPreferences copyWith({
    bool? pushNotifications,
    bool? emailNotifications,
    bool? smsNotifications,
    bool? bookingAlerts,
    bool? paymentAlerts,
    bool? teamUpdates,
    bool? marketingEmails,
    bool? systemUpdates,
    DateTime? updatedAt,
  }) {
    return NotificationPreferences(
      pushNotifications: pushNotifications ?? this.pushNotifications,
      emailNotifications: emailNotifications ?? this.emailNotifications,
      smsNotifications: smsNotifications ?? this.smsNotifications,
      bookingAlerts: bookingAlerts ?? this.bookingAlerts,
      paymentAlerts: paymentAlerts ?? this.paymentAlerts,
      teamUpdates: teamUpdates ?? this.teamUpdates,
      marketingEmails: marketingEmails ?? this.marketingEmails,
      systemUpdates: systemUpdates ?? this.systemUpdates,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
