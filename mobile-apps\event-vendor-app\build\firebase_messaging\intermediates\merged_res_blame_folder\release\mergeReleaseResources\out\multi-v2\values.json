{"logs": [{"outputFile": "io.flutter.plugins.firebase.messaging.firebase_messaging-mergeReleaseResources-29:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\29f4148e3f4cff3bdb0cd8d27cbcceed\\transformed\\jetified-activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "130,149", "startColumns": "4,4", "startOffsets": "6643,7652", "endColumns": "41,59", "endOffsets": "6680,7707"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\802b34e8110e305db18176958a5b741a\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "150", "startColumns": "4", "startOffsets": "7712", "endColumns": "53", "endOffsets": "7761"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\a0da651b055ec22709cf91944d817277\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "151", "startColumns": "4", "startOffsets": "7766", "endColumns": "49", "endOffsets": "7811"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\d51cd53175e633a38dc9073f471970fc\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "25,55,56,57,58,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,128,129,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,154,156,157,158,159,160,161,162,182,183,184,188,189,193,194,195,207,213,223,256,286,319", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "775,1773,1845,1933,1998,2735,2804,2867,2937,3005,3077,3147,3208,3282,3355,3416,3477,3539,3603,3665,3726,3794,3894,3954,4020,4093,4162,4219,4271,4333,4405,4481,4546,4605,4664,4724,4784,4844,4904,4964,5024,5084,5144,5204,5264,5323,5383,5443,5503,5563,5623,5683,5743,5803,5863,5923,5982,6042,6102,6161,6220,6279,6338,6397,6573,6608,6750,6805,6868,6923,6981,7039,7100,7163,7220,7271,7321,7382,7439,7505,7539,7574,7948,8101,8168,8240,8309,8378,8452,8524,11027,11098,11215,11416,11526,11727,11856,11928,12348,12551,12852,14583,15583,16265", "endLines": "25,55,56,57,58,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,128,129,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,154,156,157,158,159,160,161,162,182,183,187,188,192,193,194,195,212,222,255,276,318,324", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "830,1840,1928,1993,2059,2799,2862,2932,3000,3072,3142,3203,3277,3350,3411,3472,3534,3598,3660,3721,3789,3889,3949,4015,4088,4157,4214,4266,4328,4400,4476,4541,4600,4659,4719,4779,4839,4899,4959,5019,5079,5139,5199,5259,5318,5378,5438,5498,5558,5618,5678,5738,5798,5858,5918,5977,6037,6097,6156,6215,6274,6333,6392,6451,6603,6638,6800,6863,6918,6976,7034,7095,7158,7215,7266,7316,7377,7434,7500,7534,7569,7604,8013,8163,8235,8304,8373,8447,8519,8607,11093,11210,11411,11521,11722,11851,11923,11990,12546,12847,14578,15259,16260,16427"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c74c3bd83599f2958f8a4a731ded7c1a\\transformed\\jetified-firebase-messaging-24.1.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "181", "startColumns": "4", "startOffsets": "10945", "endColumns": "81", "endOffsets": "11022"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\1d36d91566f9b4b5817ac784a92dfcc1\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "127,131,152,277,282", "startColumns": "4,4,4,4,4", "startOffsets": "6516,6685,7816,15264,15434", "endLines": "127,131,152,281,285", "endColumns": "56,64,63,24,24", "endOffsets": "6568,6745,7875,15429,15578"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\e61310c2252f628562eb456e18cc7e06\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "155", "startColumns": "4", "startOffsets": "8018", "endColumns": "82", "endOffsets": "8096"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\56d4b5a4152a477167a85f04495df4f7\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "148", "startColumns": "4", "startOffsets": "7609", "endColumns": "42", "endOffsets": "7647"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\ca62d0075f4548d68d3dd8bc42db26f1\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,3,9,17,26,38,44,50,51,52,53,54,126,196,202,357,365,380", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,164,337,556,835,1149,1337,1524,1577,1637,1689,1734,6456,11995,12190,17164,17446,18060", "endLines": "2,8,16,24,37,43,49,50,51,52,53,54,126,201,206,364,379,395", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,332,551,770,1144,1332,1519,1572,1632,1684,1729,1768,6511,12185,12343,17441,18055,18709"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\e635047f1d7f095c018a5f117412df59\\transformed\\jetified-play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "59,60,61,62,63,64,65,66,163,164,165,166,167,168,169,170,172,173,174,175,176,177,178,179,180,325,338", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2064,2154,2234,2324,2414,2494,2575,2655,8612,8717,8898,9023,9130,9310,9433,9549,9819,10007,10112,10293,10418,10593,10741,10804,10866,16432,16747", "endLines": "59,60,61,62,63,64,65,66,163,164,165,166,167,168,169,170,172,173,174,175,176,177,178,179,180,337,356", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "2149,2229,2319,2409,2489,2570,2650,2730,8712,8893,9018,9125,9305,9428,9544,9647,10002,10107,10288,10413,10588,10736,10799,10861,10940,16742,17159"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\48c8606bfcca6d333ba9734a73c1c09c\\transformed\\jetified-play-services-basement-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "153,171", "startColumns": "4,4", "startOffsets": "7880,9652", "endColumns": "67,166", "endOffsets": "7943,9814"}}]}, {"outputFile": "io.flutter.plugins.firebase.messaging.firebase_messaging-release-31:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\29f4148e3f4cff3bdb0cd8d27cbcceed\\transformed\\jetified-activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "130,149", "startColumns": "4,4", "startOffsets": "6643,7652", "endColumns": "41,59", "endOffsets": "6680,7707"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\802b34e8110e305db18176958a5b741a\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "150", "startColumns": "4", "startOffsets": "7712", "endColumns": "53", "endOffsets": "7761"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\a0da651b055ec22709cf91944d817277\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "151", "startColumns": "4", "startOffsets": "7766", "endColumns": "49", "endOffsets": "7811"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\d51cd53175e633a38dc9073f471970fc\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "25,55,56,57,58,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,128,129,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,154,156,157,158,159,160,161,162,182,183,184,188,189,193,194,195,207,213,223,256,286,319", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "775,1773,1845,1933,1998,2735,2804,2867,2937,3005,3077,3147,3208,3282,3355,3416,3477,3539,3603,3665,3726,3794,3894,3954,4020,4093,4162,4219,4271,4333,4405,4481,4546,4605,4664,4724,4784,4844,4904,4964,5024,5084,5144,5204,5264,5323,5383,5443,5503,5563,5623,5683,5743,5803,5863,5923,5982,6042,6102,6161,6220,6279,6338,6397,6573,6608,6750,6805,6868,6923,6981,7039,7100,7163,7220,7271,7321,7382,7439,7505,7539,7574,7948,8101,8168,8240,8309,8378,8452,8524,11027,11098,11215,11416,11526,11727,11856,11928,12348,12551,12852,14583,15583,16265", "endLines": "25,55,56,57,58,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,128,129,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,154,156,157,158,159,160,161,162,182,183,187,188,192,193,194,195,212,222,255,276,318,324", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "830,1840,1928,1993,2059,2799,2862,2932,3000,3072,3142,3203,3277,3350,3411,3472,3534,3598,3660,3721,3789,3889,3949,4015,4088,4157,4214,4266,4328,4400,4476,4541,4600,4659,4719,4779,4839,4899,4959,5019,5079,5139,5199,5259,5318,5378,5438,5498,5558,5618,5678,5738,5798,5858,5918,5977,6037,6097,6156,6215,6274,6333,6392,6451,6603,6638,6800,6863,6918,6976,7034,7095,7158,7215,7266,7316,7377,7434,7500,7534,7569,7604,8013,8163,8235,8304,8373,8447,8519,8607,11093,11210,11411,11521,11722,11851,11923,11990,12546,12847,14578,15259,16260,16427"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c74c3bd83599f2958f8a4a731ded7c1a\\transformed\\jetified-firebase-messaging-24.1.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "181", "startColumns": "4", "startOffsets": "10945", "endColumns": "81", "endOffsets": "11022"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\1d36d91566f9b4b5817ac784a92dfcc1\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "127,131,152,277,282", "startColumns": "4,4,4,4,4", "startOffsets": "6516,6685,7816,15264,15434", "endLines": "127,131,152,281,285", "endColumns": "56,64,63,24,24", "endOffsets": "6568,6745,7875,15429,15578"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\e61310c2252f628562eb456e18cc7e06\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "155", "startColumns": "4", "startOffsets": "8018", "endColumns": "82", "endOffsets": "8096"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\56d4b5a4152a477167a85f04495df4f7\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "148", "startColumns": "4", "startOffsets": "7609", "endColumns": "42", "endOffsets": "7647"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\ca62d0075f4548d68d3dd8bc42db26f1\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,3,9,17,26,38,44,50,51,52,53,54,126,196,202,357,365,380", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,164,337,556,835,1149,1337,1524,1577,1637,1689,1734,6456,11995,12190,17164,17446,18060", "endLines": "2,8,16,24,37,43,49,50,51,52,53,54,126,201,206,364,379,395", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,332,551,770,1144,1332,1519,1572,1632,1684,1729,1768,6511,12185,12343,17441,18055,18709"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\e635047f1d7f095c018a5f117412df59\\transformed\\jetified-play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "59,60,61,62,63,64,65,66,163,164,165,166,167,168,169,170,172,173,174,175,176,177,178,179,180,325,338", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2064,2154,2234,2324,2414,2494,2575,2655,8612,8717,8898,9023,9130,9310,9433,9549,9819,10007,10112,10293,10418,10593,10741,10804,10866,16432,16747", "endLines": "59,60,61,62,63,64,65,66,163,164,165,166,167,168,169,170,172,173,174,175,176,177,178,179,180,337,356", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "2149,2229,2319,2409,2489,2570,2650,2730,8712,8893,9018,9125,9305,9428,9544,9647,10002,10107,10288,10413,10588,10736,10799,10861,10940,16742,17159"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\48c8606bfcca6d333ba9734a73c1c09c\\transformed\\jetified-play-services-basement-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "153,171", "startColumns": "4,4", "startOffsets": "7880,9652", "endColumns": "67,166", "endOffsets": "7943,9814"}}]}]}