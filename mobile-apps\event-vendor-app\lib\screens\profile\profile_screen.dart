import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/vendor_auth_provider.dart';
import '../../config/app_theme.dart';
import '../../models/vendor_model.dart';
import '../../models/notification_preferences_model.dart';
import '../../services/notification_preferences_service.dart';
import '../../widgets/app_logo.dart';
import 'edit_profile_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<VendorAuthProvider>(context);
    final vendor = authProvider.currentVendor;

    if (vendor == null) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Transform.translate(
              offset: const Offset(-8, 0), // Move logo 8px to the left
              child: const AppLogo(
                size: AppLogoSizes.header,
                variant: LogoVariant.white,
                showBackground: false,
              ),
            ),
            const SizedBox(width: 4), // Reduced from 12 to 4
            const Text('Profile'),
          ],
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _showEditProfileDialog(vendor),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Profile Header
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.primaryColor,
                    AppTheme.primaryColor.withOpacity(0.8),
                  ],
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    // Profile Picture
                    Stack(
                      children: [
                        CircleAvatar(
                          radius: 60,
                          backgroundColor: Colors.white,
                          child: vendor.photoURL != null
                              ? ClipRRect(
                                  borderRadius: BorderRadius.circular(60),
                                  child: Image.network(
                                    vendor.photoURL!,
                                    width: 120,
                                    height: 120,
                                    fit: BoxFit.cover,
                                  ),
                                )
                              : Icon(
                                  Icons.person,
                                  size: 60,
                                  color: AppTheme.primaryColor,
                                ),
                        ),
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: Container(
                            decoration: const BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            ),
                            child: IconButton(
                              icon: Icon(Icons.camera_alt,
                                  color: AppTheme.primaryColor),
                              onPressed: () => _changeProfilePicture(),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Name and Business
                    Text(
                      vendor.profile.businessName.isNotEmpty
                          ? vendor.profile.businessName
                          : 'Business Name',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${vendor.profile.firstName} ${vendor.profile.lastName}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                      ),
                    ),
                    const SizedBox(height: 8),

                    // Verification Status
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        _buildVerificationChip(
                          'Email',
                          vendor.isVerified,
                          Icons.email,
                        ),
                        const SizedBox(width: 8),
                        _buildVerificationChip(
                          'Aadhar',
                          vendor.aadhaarVerified,
                          Icons.credit_card,
                        ),
                        const SizedBox(width: 8),
                        _buildVerificationChip(
                          'Approved',
                          vendor.isApproved,
                          Icons.verified,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // Profile Sections
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Personal Information
                  _buildSection(
                    'Personal Information',
                    Icons.person,
                    [
                      _buildInfoTile('Full Name',
                          '${vendor.profile.firstName} ${vendor.profile.lastName}'),
                      _buildInfoTile('Email', vendor.email),
                      _buildInfoTile(
                          'Phone', vendor.phoneNumber ?? 'Not provided'),
                      _buildInfoTile(
                          'Display Name', vendor.displayName ?? 'Not provided'),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Business Information
                  _buildSection(
                    'Business Information',
                    Icons.business,
                    [
                      _buildInfoTile(
                          'Business Name', vendor.profile.businessName),
                      _buildInfoTile(
                          'Description',
                          vendor.profile.description.isNotEmpty
                              ? vendor.profile.description
                              : 'Not provided'),
                      _buildInfoTile(
                          'License Number',
                          vendor.businessDetails.licenseNumber.isNotEmpty
                              ? vendor.businessDetails.licenseNumber
                              : 'Not provided'),
                      _buildInfoTile('Established Year',
                          vendor.businessDetails.establishedYear.toString()),
                      _buildInfoTile('Employee Count',
                          vendor.businessDetails.employeeCount.toString()),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Address Information
                  _buildSection(
                    'Address',
                    Icons.location_on,
                    [
                      _buildInfoTile(
                          'Street',
                          vendor.businessDetails.businessAddress.street
                                  .isNotEmpty
                              ? vendor.businessDetails.businessAddress.street
                              : 'Not provided'),
                      _buildInfoTile(
                          'City',
                          vendor.businessDetails.businessAddress.city.isNotEmpty
                              ? vendor.businessDetails.businessAddress.city
                              : 'Not provided'),
                      _buildInfoTile(
                          'State',
                          vendor.businessDetails.businessAddress.state
                                  .isNotEmpty
                              ? vendor.businessDetails.businessAddress.state
                              : 'Not provided'),
                      _buildInfoTile('Country',
                          vendor.businessDetails.businessAddress.country),
                      _buildInfoTile(
                          'Pincode',
                          vendor.businessDetails.businessAddress.pincode
                                  .isNotEmpty
                              ? vendor.businessDetails.businessAddress.pincode
                              : 'Not provided'),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Services & Specializations
                  _buildSection(
                    'Services & Specializations',
                    Icons.star,
                    [
                      _buildChipsTile(
                          'Service Areas', vendor.profile.serviceAreas),
                      _buildChipsTile(
                          'Specializations', vendor.profile.specializations),
                      _buildChipsTile('Event Types', vendor.profile.eventTypes),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Statistics
                  _buildSection(
                    'Statistics',
                    Icons.analytics,
                    [
                      _buildInfoTile(
                          'Total Events', vendor.stats.totalEvents.toString()),
                      _buildInfoTile('Completed Events',
                          vendor.stats.completedEvents.toString()),
                      _buildInfoTile('Average Rating',
                          vendor.stats.averageRating.toStringAsFixed(1)),
                      _buildInfoTile('Total Reviews',
                          vendor.stats.totalReviews.toString()),
                      _buildInfoTile('Total Earnings',
                          '₹${vendor.stats.totalEarnings.toStringAsFixed(0)}'),
                      _buildInfoTile('Completion Rate',
                          '${vendor.stats.completionRate.toStringAsFixed(1)}%'),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Account Actions
                  _buildSection(
                    'Account Actions',
                    Icons.settings,
                    [
                      _buildActionTile('Change Password', Icons.lock,
                          () => _changePassword()),
                      _buildActionTile('Notification Settings',
                          Icons.notifications, () => _notificationSettings()),
                      _buildActionTile('Privacy Settings', Icons.privacy_tip,
                          () => _privacySettings()),
                      _buildActionTile(
                          'Help & Support', Icons.help, () => _helpSupport()),
                      _buildActionTile(
                          'Sign Out', Icons.logout, () => _signOut(),
                          isDestructive: true),
                    ],
                  ),

                  const SizedBox(height: 32),

                  // App Version
                  Text(
                    'Version 1.0.0',
                    style: TextStyle(
                      color: Colors.grey.shade500,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVerificationChip(String label, bool isVerified, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isVerified
            ? Colors.green.withOpacity(0.2)
            : Colors.red.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isVerified ? Colors.green : Colors.red,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isVerified ? Icons.check_circle : Icons.cancel,
            size: 14,
            color: isVerified ? Colors.green : Colors.red,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: isVerified ? Colors.green : Colors.red,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, IconData icon, List<Widget> children) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoTile(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChipsTile(String label, List<String> items) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 8),
          items.isEmpty
              ? const Text('Not specified',
                  style: TextStyle(color: Colors.grey))
              : Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: items
                      .map((item) => Chip(
                            label: Text(item,
                                style: const TextStyle(fontSize: 12)),
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                          ))
                      .toList(),
                ),
        ],
      ),
    );
  }

  Widget _buildActionTile(String title, IconData icon, VoidCallback onTap,
      {bool isDestructive = false}) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? Colors.red : AppTheme.primaryColor,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isDestructive ? Colors.red : null,
        ),
      ),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }

  void _showEditProfileDialog(VendorModel vendor) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const EditProfileScreen(),
      ),
    );
  }

  void _changeProfilePicture() {
    // TODO: Implement profile picture change
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Change profile picture feature coming soon!'),
      ),
    );
  }

  void _changePassword() {
    final formKey = GlobalKey<FormState>();
    final currentPasswordController = TextEditingController();
    final newPasswordController = TextEditingController();
    final confirmPasswordController = TextEditingController();
    bool obscureCurrentPassword = true;
    bool obscureNewPassword = true;
    bool obscureConfirmPassword = true;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Change Password'),
          content: SizedBox(
            width: double.maxFinite,
            child: Form(
              key: formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Current Password
                  TextFormField(
                    controller: currentPasswordController,
                    obscureText: obscureCurrentPassword,
                    decoration: InputDecoration(
                      labelText: 'Current Password *',
                      border: const OutlineInputBorder(),
                      suffixIcon: IconButton(
                        icon: Icon(
                          obscureCurrentPassword
                              ? Icons.visibility
                              : Icons.visibility_off,
                        ),
                        onPressed: () {
                          setState(() {
                            obscureCurrentPassword = !obscureCurrentPassword;
                          });
                        },
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your current password';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // New Password
                  TextFormField(
                    controller: newPasswordController,
                    obscureText: obscureNewPassword,
                    decoration: InputDecoration(
                      labelText: 'New Password *',
                      border: const OutlineInputBorder(),
                      suffixIcon: IconButton(
                        icon: Icon(
                          obscureNewPassword
                              ? Icons.visibility
                              : Icons.visibility_off,
                        ),
                        onPressed: () {
                          setState(() {
                            obscureNewPassword = !obscureNewPassword;
                          });
                        },
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a new password';
                      }
                      if (value.length < 8) {
                        return 'Password must be at least 8 characters long';
                      }
                      if (!RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)')
                          .hasMatch(value)) {
                        return 'Password must contain uppercase, lowercase, and number';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Confirm Password
                  TextFormField(
                    controller: confirmPasswordController,
                    obscureText: obscureConfirmPassword,
                    decoration: InputDecoration(
                      labelText: 'Confirm New Password *',
                      border: const OutlineInputBorder(),
                      suffixIcon: IconButton(
                        icon: Icon(
                          obscureConfirmPassword
                              ? Icons.visibility
                              : Icons.visibility_off,
                        ),
                        onPressed: () {
                          setState(() {
                            obscureConfirmPassword = !obscureConfirmPassword;
                          });
                        },
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please confirm your new password';
                      }
                      if (value != newPasswordController.text) {
                        return 'Passwords do not match';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Password Requirements
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Password Requirements:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade800,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '• At least 8 characters long\n• Contains uppercase letter\n• Contains lowercase letter\n• Contains at least one number',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (formKey.currentState!.validate()) {
                  // Show loading indicator
                  showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (context) => const Center(
                      child: CircularProgressIndicator(),
                    ),
                  );

                  try {
                    final authProvider =
                        Provider.of<VendorAuthProvider>(context, listen: false);
                    final success = await authProvider.changePassword(
                      currentPasswordController.text.trim(),
                      newPasswordController.text.trim(),
                    );

                    if (!mounted) return;

                    // Close loading dialog
                    Navigator.of(context).pop();

                    if (success) {
                      // Close password dialog
                      Navigator.of(context).pop();

                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Password changed successfully!'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                              'Failed to change password. Please check your current password.'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  } catch (e) {
                    if (!mounted) return;

                    // Close loading dialog
                    Navigator.of(context).pop();

                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Error changing password: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              child: const Text('Change Password'),
            ),
          ],
        ),
      ),
    );
  }

  void _notificationSettings() async {
    final authProvider =
        Provider.of<VendorAuthProvider>(context, listen: false);
    final vendor = authProvider.currentVendor;

    if (vendor == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please log in to access notification settings'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final notificationService = NotificationPreferencesService();

    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      // Load current preferences
      final preferences =
          await notificationService.getNotificationPreferences(vendor.uid);

      if (!mounted) return;

      // Close loading dialog
      Navigator.of(context).pop();

      // Show settings dialog with current preferences
      _showNotificationSettingsDialog(
          vendor.uid, preferences, notificationService);
    } catch (e) {
      if (!mounted) return;

      // Close loading dialog
      Navigator.of(context).pop();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading notification settings: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showNotificationSettingsDialog(
    String vendorId,
    NotificationPreferences initialPreferences,
    NotificationPreferencesService notificationService,
  ) {
    // Local state for the dialog
    NotificationPreferences preferences = initialPreferences;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Notification Settings'),
          content: SizedBox(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'General Notifications',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  SwitchListTile(
                    title: const Text('Push Notifications'),
                    subtitle:
                        const Text('Receive notifications on your device'),
                    value: preferences.pushNotifications,
                    onChanged: (value) {
                      setState(() {
                        preferences =
                            preferences.copyWith(pushNotifications: value);
                      });
                    },
                  ),
                  SwitchListTile(
                    title: const Text('Email Notifications'),
                    subtitle: const Text('Receive notifications via email'),
                    value: preferences.emailNotifications,
                    onChanged: (value) {
                      setState(() {
                        preferences =
                            preferences.copyWith(emailNotifications: value);
                      });
                    },
                  ),
                  SwitchListTile(
                    title: const Text('SMS Notifications'),
                    subtitle: const Text('Receive notifications via SMS'),
                    value: preferences.smsNotifications,
                    onChanged: (value) {
                      setState(() {
                        preferences =
                            preferences.copyWith(smsNotifications: value);
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Event & Booking Alerts',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  SwitchListTile(
                    title: const Text('Booking Alerts'),
                    subtitle: const Text('New bookings and booking updates'),
                    value: preferences.bookingAlerts,
                    onChanged: (value) {
                      setState(() {
                        preferences =
                            preferences.copyWith(bookingAlerts: value);
                      });
                    },
                  ),
                  SwitchListTile(
                    title: const Text('Payment Alerts'),
                    subtitle: const Text('Payment confirmations and updates'),
                    value: preferences.paymentAlerts,
                    onChanged: (value) {
                      setState(() {
                        preferences =
                            preferences.copyWith(paymentAlerts: value);
                      });
                    },
                  ),
                  SwitchListTile(
                    title: const Text('Team Updates'),
                    subtitle: const Text('Team member activity and updates'),
                    value: preferences.teamUpdates,
                    onChanged: (value) {
                      setState(() {
                        preferences = preferences.copyWith(teamUpdates: value);
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Marketing & System',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  SwitchListTile(
                    title: const Text('Marketing Emails'),
                    subtitle: const Text('Promotional offers and updates'),
                    value: preferences.marketingEmails,
                    onChanged: (value) {
                      setState(() {
                        preferences =
                            preferences.copyWith(marketingEmails: value);
                      });
                    },
                  ),
                  SwitchListTile(
                    title: const Text('System Updates'),
                    subtitle: const Text('App updates and maintenance notices'),
                    value: preferences.systemUpdates,
                    onChanged: (value) {
                      setState(() {
                        preferences =
                            preferences.copyWith(systemUpdates: value);
                      });
                    },
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                // Show loading
                showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (context) => const Center(
                    child: CircularProgressIndicator(),
                  ),
                );

                try {
                  final success =
                      await notificationService.updateNotificationPreferences(
                    vendorId,
                    preferences,
                  );

                  if (!mounted) return;

                  // Close loading dialog
                  Navigator.of(context).pop();

                  if (success) {
                    // Close settings dialog
                    Navigator.of(context).pop();

                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content:
                            Text('Notification settings saved successfully!'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Failed to save notification settings'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                } catch (e) {
                  if (!mounted) return;

                  // Close loading dialog
                  Navigator.of(context).pop();

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error saving settings: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              child: const Text('Save Settings'),
            ),
          ],
        ),
      ),
    );
  }

  void _privacySettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Privacy Settings'),
        content: SizedBox(
          width: double.maxFinite,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Data Management',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: 16),
                ListTile(
                  leading: const Icon(Icons.download, color: Colors.blue),
                  title: const Text('Download My Data'),
                  subtitle: const Text('Get a copy of your account data'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    Navigator.of(context).pop();
                    _downloadUserData();
                  },
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.visibility, color: Colors.orange),
                  title: const Text('Data Visibility'),
                  subtitle: const Text('Control who can see your information'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    Navigator.of(context).pop();
                    _dataVisibilitySettings();
                  },
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.delete_forever, color: Colors.red),
                  title: const Text('Delete Account'),
                  subtitle:
                      const Text('Permanently delete your account and data'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    Navigator.of(context).pop();
                    _confirmDeleteAccount();
                  },
                ),
                const SizedBox(height: 16),
                Text(
                  'Privacy Policy',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: 16),
                ListTile(
                  leading: const Icon(Icons.policy, color: Colors.green),
                  title: const Text('Privacy Policy'),
                  subtitle: const Text('Read our privacy policy'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    Navigator.of(context).pop();
                    _showPrivacyPolicy();
                  },
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.gavel, color: Colors.purple),
                  title: const Text('Terms of Service'),
                  subtitle: const Text('Read our terms of service'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    Navigator.of(context).pop();
                    _showTermsOfService();
                  },
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _helpSupport() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Help & Support'),
        content: SizedBox(
          width: double.maxFinite,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Frequently Asked Questions',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: 16),
                ListTile(
                  leading: const Icon(Icons.help_outline, color: Colors.blue),
                  title: const Text('How to manage bookings?'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    Navigator.of(context).pop();
                    _showFAQAnswer(
                        'Managing Bookings',
                        'You can view and manage all your bookings from the Bookings tab. '
                            'Click on any booking to see details, update status, or communicate with customers.');
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.people, color: Colors.green),
                  title: const Text('How to add team members?'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    Navigator.of(context).pop();
                    _showFAQAnswer(
                        'Adding Team Members',
                        'Go to the Team section and click the + button to add new team members. '
                            'Fill in their details including role, department, and contact information.');
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.payment, color: Colors.orange),
                  title: const Text('Payment and billing issues?'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    Navigator.of(context).pop();
                    _showFAQAnswer(
                        'Payment & Billing',
                        'For payment issues, check your payment methods in settings. '
                            'Contact support if you have billing disputes or need payment assistance.');
                  },
                ),
                const SizedBox(height: 16),
                Text(
                  'Contact Support',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: 16),
                ListTile(
                  leading: const Icon(Icons.email, color: Colors.red),
                  title: const Text('Email Support'),
                  subtitle: const Text('<EMAIL>'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    Navigator.of(context).pop();
                    _contactEmailSupport();
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.phone, color: Colors.green),
                  title: const Text('Phone Support'),
                  subtitle: const Text('+91 1800-123-4567'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    Navigator.of(context).pop();
                    _contactPhoneSupport();
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.chat, color: Colors.blue),
                  title: const Text('Live Chat'),
                  subtitle: const Text('Chat with our support team'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    Navigator.of(context).pop();
                    _startLiveChat();
                  },
                ),
                const SizedBox(height: 16),
                Text(
                  'Resources',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: 16),
                ListTile(
                  leading: const Icon(Icons.book, color: Colors.purple),
                  title: const Text('User Guide'),
                  subtitle: const Text('Complete app documentation'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    Navigator.of(context).pop();
                    _showUserGuide();
                  },
                ),
                ListTile(
                  leading:
                      const Icon(Icons.video_library, color: Colors.orange),
                  title: const Text('Video Tutorials'),
                  subtitle: const Text('Learn with step-by-step videos'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    Navigator.of(context).pop();
                    _showVideoTutorials();
                  },
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _signOut() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();

              // Show loading indicator
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => const Center(
                  child: CircularProgressIndicator(),
                ),
              );

              try {
                final authProvider =
                    Provider.of<VendorAuthProvider>(context, listen: false);
                await authProvider.signOut();

                if (!mounted) return;

                // Close loading dialog
                Navigator.of(context).pop();

                // Navigate to login screen and clear all previous routes
                Navigator.of(context).pushNamedAndRemoveUntil(
                  '/vendor-login',
                  (route) => false,
                );
              } catch (e) {
                if (!mounted) return;

                // Close loading dialog
                Navigator.of(context).pop();

                // Show error message
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Sign out failed: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child:
                const Text('Sign Out', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _downloadUserData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content:
            Text('Data download will be sent to your email within 24 hours'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _dataVisibilitySettings() {
    bool profileVisible = true;
    bool contactVisible = false;
    bool businessInfoVisible = true;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Data Visibility Settings'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SwitchListTile(
                title: const Text('Profile Visibility'),
                subtitle: const Text('Make your profile visible to customers'),
                value: profileVisible,
                onChanged: (value) {
                  setState(() {
                    profileVisible = value;
                  });
                },
              ),
              SwitchListTile(
                title: const Text('Contact Information'),
                subtitle: const Text('Show contact details publicly'),
                value: contactVisible,
                onChanged: (value) {
                  setState(() {
                    contactVisible = value;
                  });
                },
              ),
              SwitchListTile(
                title: const Text('Business Information'),
                subtitle: const Text('Display business details'),
                value: businessInfoVisible,
                onChanged: (value) {
                  setState(() {
                    businessInfoVisible = value;
                  });
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Visibility settings updated!'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              child: const Text('Save'),
            ),
          ],
        ),
      ),
    );
  }

  void _confirmDeleteAccount() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Account'),
        content: const Text(
          'Are you sure you want to permanently delete your account? This action cannot be undone and all your data will be lost.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                      'Account deletion request submitted. You will receive a confirmation email.'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete Account',
                style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showPrivacyPolicy() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Privacy Policy'),
        content: const SingleChildScrollView(
          child: Text(
            'Privacy Policy\n\n'
            'We are committed to protecting your privacy and personal information. '
            'This policy explains how we collect, use, and protect your data.\n\n'
            '1. Information Collection\n'
            'We collect information you provide directly to us, such as when you create an account, '
            'update your profile, or contact us.\n\n'
            '2. Information Use\n'
            'We use your information to provide and improve our services, communicate with you, '
            'and ensure security.\n\n'
            '3. Information Sharing\n'
            'We do not sell or rent your personal information to third parties.\n\n'
            '4. Data Security\n'
            'We implement appropriate security measures to protect your information.\n\n'
            'For the complete privacy policy, please visit our website.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showTermsOfService() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Terms of Service'),
        content: const SingleChildScrollView(
          child: Text(
            'Terms of Service\n\n'
            'By using our service, you agree to these terms.\n\n'
            '1. Service Usage\n'
            'You may use our service for lawful purposes only.\n\n'
            '2. Account Responsibility\n'
            'You are responsible for maintaining the security of your account.\n\n'
            '3. Content Guidelines\n'
            'You must not post inappropriate or harmful content.\n\n'
            '4. Service Availability\n'
            'We strive to maintain service availability but cannot guarantee 100% uptime.\n\n'
            '5. Termination\n'
            'We may terminate accounts that violate these terms.\n\n'
            'For the complete terms of service, please visit our website.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showFAQAnswer(String title, String answer) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(answer),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _contactEmailSupport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Opening email client to contact support...'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _contactPhoneSupport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Calling support: +91 1800-123-4567'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _startLiveChat() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Starting live chat with support team...'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _showUserGuide() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('User Guide'),
        content: const SingleChildScrollView(
          child: Text(
            'Event Vendor App User Guide\n\n'
            '1. Getting Started\n'
            '• Complete your profile setup\n'
            '• Add your business information\n'
            '• Upload photos of your services\n\n'
            '2. Managing Bookings\n'
            '• View all bookings in the Bookings tab\n'
            '• Update booking status\n'
            '• Communicate with customers\n\n'
            '3. Team Management\n'
            '• Add team members with roles\n'
            '• Assign tasks to team members\n'
            '• Track team performance\n\n'
            '4. Profile Settings\n'
            '• Update business details\n'
            '• Manage notification preferences\n'
            '• Configure privacy settings\n\n'
            'For detailed instructions, visit our website.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showVideoTutorials() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Opening video tutorials in browser...'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}
