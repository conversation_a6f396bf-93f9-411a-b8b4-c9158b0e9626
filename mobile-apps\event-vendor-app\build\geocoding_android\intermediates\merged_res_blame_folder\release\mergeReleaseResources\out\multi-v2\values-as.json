{"logs": [{"outputFile": "com.baseflow.geocoding.geocoding_android-release-25:/values-as/values-as.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\d51cd53175e633a38dc9073f471970fc\\transformed\\core-1.13.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}}]}, {"outputFile": "com.baseflow.geocoding.geocoding_android-mergeReleaseResources-23:/values-as/values-as.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\d51cd53175e633a38dc9073f471970fc\\transformed\\core-1.13.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}}]}]}