import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../config/app_theme.dart';
import '../../models/event_model.dart';
import '../../services/event_service.dart';
import '../../services/image_upload_service.dart';
import '../../providers/vendor_auth_provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package_management_screen.dart';

class AddEventScreen extends StatefulWidget {
  final EventCategory? initialCategory;
  final EventModel? eventToEdit;

  const AddEventScreen({
    super.key,
    this.initialCategory,
    this.eventToEdit,
  });

  @override
  State<AddEventScreen> createState() => _AddEventScreenState();
}

class _AddEventScreenState extends State<AddEventScreen> {
  final _formKey = GlobalKey<FormState>();
  final EventService _eventService = EventService();
  final ImageUploadService _imageUploadService = ImageUploadService();

  // Form controllers
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _basePriceController = TextEditingController();
  final TextEditingController _estimatedGuestsController =
      TextEditingController();
  final TextEditingController _venueNameController = TextEditingController();
  final TextEditingController _venueAddressController = TextEditingController();
  final TextEditingController _tagsController = TextEditingController();

  // Form state
  EventCategory _selectedCategory = EventCategory.wedding;
  EventStatus _selectedStatus = EventStatus.draft;
  DateTime? _eventDate;
  DateTime? _eventEndDate;
  List<String> _imageUrls = [];
  List<EventPackage> _packages = [];
  Map<String, dynamic> _requirements = {};
  bool _isLoading = false;
  bool _isUploading = false;

  @override
  void initState() {
    super.initState();
    _selectedCategory = widget.initialCategory ?? EventCategory.wedding;

    if (widget.eventToEdit != null) {
      _populateFormWithEvent(widget.eventToEdit!);
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _basePriceController.dispose();
    _estimatedGuestsController.dispose();
    _venueNameController.dispose();
    _venueAddressController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  void _populateFormWithEvent(EventModel event) {
    _titleController.text = event.title;
    _descriptionController.text = event.description;
    _basePriceController.text = event.basePrice.toString();
    _estimatedGuestsController.text = event.estimatedGuests.toString();
    _venueNameController.text = event.venue['name'] ?? '';
    _venueAddressController.text = event.venue['address'] ?? '';
    _tagsController.text = event.tags.join(', ');

    setState(() {
      _selectedCategory = event.category;
      _selectedStatus = event.status;
      _eventDate = event.eventDate;
      _eventEndDate = event.eventEndDate;
      _imageUrls = List.from(event.imageUrls);
      _packages = List.from(event.packages);
      _requirements = Map.from(event.requirements);
    });
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.eventToEdit != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? 'Edit Event' : 'Add New Event'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            )
          else
            TextButton(
              onPressed: _saveEvent,
              child: Text(
                isEditing ? 'Update' : 'Save',
                style: const TextStyle(color: Colors.white),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppTheme.spacingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Basic Information Section
              _buildSectionHeader('Basic Information'),
              const SizedBox(height: AppTheme.spacingM),

              // Event Title
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'Event Title *',
                  hintText: 'Enter event title',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Event title is required';
                  }
                  return null;
                },
              ),
              const SizedBox(height: AppTheme.spacingM),

              // Event Category
              DropdownButtonFormField<EventCategory>(
                value: _selectedCategory,
                decoration: const InputDecoration(
                  labelText: 'Event Category *',
                  border: OutlineInputBorder(),
                ),
                items: EventCategory.values.map((category) {
                  return DropdownMenuItem(
                    value: category,
                    child: Text(_getCategoryDisplayName(category)),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedCategory = value;
                    });
                  }
                },
              ),
              const SizedBox(height: AppTheme.spacingM),

              // Event Description
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Event Description *',
                  hintText: 'Describe your event...',
                  border: OutlineInputBorder(),
                ),
                maxLines: 4,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Event description is required';
                  }
                  return null;
                },
              ),
              const SizedBox(height: AppTheme.spacingL),

              // Pricing & Capacity Section
              _buildSectionHeader('Pricing & Capacity'),
              const SizedBox(height: AppTheme.spacingM),

              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _basePriceController,
                      decoration: const InputDecoration(
                        labelText: 'Base Price (₹) *',
                        hintText: '50000',
                        border: OutlineInputBorder(),
                        prefixText: '₹ ',
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Base price is required';
                        }
                        final price = double.tryParse(value);
                        if (price == null || price <= 0) {
                          return 'Enter valid price';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: AppTheme.spacingM),
                  Expanded(
                    child: TextFormField(
                      controller: _estimatedGuestsController,
                      decoration: const InputDecoration(
                        labelText: 'Estimated Guests *',
                        hintText: '100',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Guest count required';
                        }
                        final guests = int.tryParse(value);
                        if (guests == null || guests <= 0) {
                          return 'Enter valid count';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppTheme.spacingL),

              // Event Dates Section
              _buildSectionHeader('Event Dates'),
              const SizedBox(height: AppTheme.spacingM),

              Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () => _selectDate(context, true),
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'Event Start Date',
                          border: OutlineInputBorder(),
                          suffixIcon: Icon(Icons.calendar_today),
                        ),
                        child: Text(
                          _eventDate != null
                              ? '${_eventDate!.day}/${_eventDate!.month}/${_eventDate!.year}'
                              : 'Select date',
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: AppTheme.spacingM),
                  Expanded(
                    child: InkWell(
                      onTap: () => _selectDate(context, false),
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'Event End Date',
                          border: OutlineInputBorder(),
                          suffixIcon: Icon(Icons.calendar_today),
                        ),
                        child: Text(
                          _eventEndDate != null
                              ? '${_eventEndDate!.day}/${_eventEndDate!.month}/${_eventEndDate!.year}'
                              : 'Select date',
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppTheme.spacingL),

              // Venue Information Section
              _buildSectionHeader('Venue Information'),
              const SizedBox(height: AppTheme.spacingM),

              TextFormField(
                controller: _venueNameController,
                decoration: const InputDecoration(
                  labelText: 'Venue Name',
                  hintText: 'Enter venue name',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: AppTheme.spacingM),

              TextFormField(
                controller: _venueAddressController,
                decoration: const InputDecoration(
                  labelText: 'Venue Address',
                  hintText: 'Enter complete address',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              const SizedBox(height: AppTheme.spacingL),

              // Event Images Section
              _buildSectionHeader('Event Images'),
              const SizedBox(height: AppTheme.spacingM),

              _buildImageSection(),
              const SizedBox(height: AppTheme.spacingL),

              // Tags Section
              _buildSectionHeader('Tags'),
              const SizedBox(height: AppTheme.spacingM),

              TextFormField(
                controller: _tagsController,
                decoration: const InputDecoration(
                  labelText: 'Tags (comma separated)',
                  hintText: 'wedding, outdoor, luxury',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: AppTheme.spacingL),

              // Event Packages Section
              _buildSectionHeader('Event Packages'),
              const SizedBox(height: AppTheme.spacingM),

              _buildPackagesSection(),
              const SizedBox(height: AppTheme.spacingL),

              // Event Status Section
              _buildSectionHeader('Event Status'),
              const SizedBox(height: AppTheme.spacingM),

              DropdownButtonFormField<EventStatus>(
                value: _selectedStatus,
                decoration: const InputDecoration(
                  labelText: 'Event Status',
                  border: OutlineInputBorder(),
                ),
                items: EventStatus.values.map((status) {
                  return DropdownMenuItem(
                    value: status,
                    child: Text(_getStatusDisplayName(status)),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedStatus = value;
                    });
                  }
                },
              ),
              const SizedBox(height: AppTheme.spacingXL),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: AppTheme.headingSmall.copyWith(
        color: AppTheme.primaryColor,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildPackagesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            ElevatedButton.icon(
              onPressed: _managePackages,
              icon: const Icon(Icons.inventory_2),
              label: const Text('Manage Packages'),
            ),
            const SizedBox(width: AppTheme.spacingM),
            Text(
              '${_packages.length} package(s) created',
              style: AppTheme.bodyMedium.copyWith(color: Colors.grey[600]),
            ),
          ],
        ),
        if (_packages.isNotEmpty) ...[
          const SizedBox(height: AppTheme.spacingM),
          ...(_packages
              .map((package) => Container(
                    margin: const EdgeInsets.only(bottom: AppTheme.spacingS),
                    padding: const EdgeInsets.all(AppTheme.spacingM),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(AppTheme.radiusS),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                package.name,
                                style: AppTheme.bodyMedium
                                    .copyWith(fontWeight: FontWeight.bold),
                              ),
                              Text(
                                '₹${package.basePrice.toStringAsFixed(0)} • ${package.maxGuests} guests',
                                style: AppTheme.bodySmall
                                    .copyWith(color: Colors.grey[600]),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ))
              .toList()),
        ],
      ],
    );
  }

  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            ElevatedButton.icon(
              onPressed: _isUploading ? null : _pickImages,
              icon: _isUploading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.add_photo_alternate),
              label: Text(_isUploading ? 'Uploading...' : 'Add Images'),
            ),
            const SizedBox(width: AppTheme.spacingM),
            Text(
              '${_imageUrls.length} image(s) added',
              style: AppTheme.bodyMedium.copyWith(color: Colors.grey[600]),
            ),
          ],
        ),
        if (_imageUrls.isNotEmpty) ...[
          const SizedBox(height: AppTheme.spacingM),
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _imageUrls.length,
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.only(right: AppTheme.spacingS),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(AppTheme.radiusS),
                        child: Image.network(
                          _imageUrls[index],
                          width: 100,
                          height: 100,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 100,
                              height: 100,
                              color: Colors.grey[300],
                              child: const Icon(Icons.error),
                            );
                          },
                        ),
                      ),
                      Positioned(
                        top: 4,
                        right: 4,
                        child: GestureDetector(
                          onTap: () => _removeImage(index),
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ],
    );
  }

  Future<void> _pickImages() async {
    try {
      setState(() {
        _isUploading = true;
      });

      final List<XFile> images = await _imageUploadService.pickMultipleImages();

      if (images.isNotEmpty) {
        final vendorAuth =
            Provider.of<VendorAuthProvider>(context, listen: false);
        final vendorId = vendorAuth.currentVendor?.uid;

        if (vendorId != null) {
          final String uploadPath = _imageUploadService.getEventImagesPath(
              vendorId, 'temp_${DateTime.now().millisecondsSinceEpoch}');
          final List<String> uploadedUrls = await _imageUploadService
              .uploadMultipleImages(images, uploadPath);

          setState(() {
            _imageUrls.addAll(uploadedUrls);
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                    '${uploadedUrls.length} image(s) uploaded successfully'),
                backgroundColor: Colors.green,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error uploading images: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }

  void _removeImage(int index) {
    setState(() {
      _imageUrls.removeAt(index);
    });
  }

  void _managePackages() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PackageManagementScreen(
          initialPackages: _packages,
          onPackagesChanged: (packages) {
            setState(() {
              _packages = packages;
            });
          },
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate
          ? (_eventDate ?? DateTime.now())
          : (_eventEndDate ?? _eventDate ?? DateTime.now()),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _eventDate = picked;
          // If end date is before start date, reset it
          if (_eventEndDate != null && _eventEndDate!.isBefore(picked)) {
            _eventEndDate = null;
          }
        } else {
          _eventEndDate = picked;
        }
      });
    }
  }

  Future<void> _saveEvent() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final vendorAuth =
          Provider.of<VendorAuthProvider>(context, listen: false);
      final vendorId = vendorAuth.currentVendor?.uid;

      if (vendorId == null) {
        throw Exception('Vendor not authenticated');
      }

      // Parse tags
      final List<String> tags = _tagsController.text
          .split(',')
          .map((tag) => tag.trim())
          .where((tag) => tag.isNotEmpty)
          .toList();

      // Create venue map
      final Map<String, dynamic> venue = {
        'name': _venueNameController.text.trim(),
        'address': _venueAddressController.text.trim(),
      };

      final EventModel event = EventModel(
        id: widget.eventToEdit?.id ?? '',
        vendorId: vendorId,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        category: _selectedCategory,
        status: _selectedStatus,
        imageUrls: _imageUrls,
        packages: _packages,
        venue: venue,
        eventDate: _eventDate,
        eventEndDate: _eventEndDate,
        estimatedGuests: int.parse(_estimatedGuestsController.text),
        basePrice: double.parse(_basePriceController.text),
        requirements: _requirements,
        tags: tags,
        isActive: true,
        createdAt: widget.eventToEdit?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
        customizations: widget.eventToEdit?.customizations ?? {},
      );

      bool success;
      if (widget.eventToEdit != null) {
        success =
            await _eventService.updateEvent(widget.eventToEdit!.id, event);
      } else {
        final eventId = await _eventService.createEvent(event);
        success = eventId != null;
      }

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.eventToEdit != null
                ? 'Event updated successfully'
                : 'Event created successfully'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop(true);
      } else {
        throw Exception('Failed to save event');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving event: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String _getCategoryDisplayName(EventCategory category) {
    switch (category) {
      case EventCategory.wedding:
        return 'Wedding';
      case EventCategory.corporate:
        return 'Corporate Event';
      case EventCategory.birthday:
        return 'Birthday Party';
      case EventCategory.anniversary:
        return 'Anniversary';
      case EventCategory.engagement:
        return 'Engagement';
      case EventCategory.reception:
        return 'Reception';
      case EventCategory.conference:
        return 'Conference';
      case EventCategory.seminar:
        return 'Seminar';
      case EventCategory.workshop:
        return 'Workshop';
      case EventCategory.party:
        return 'Party';
      case EventCategory.festival:
        return 'Festival';
      case EventCategory.exhibition:
        return 'Exhibition';
      case EventCategory.launch:
        return 'Product Launch';
      case EventCategory.other:
        return 'Other';
    }
  }

  String _getStatusDisplayName(EventStatus status) {
    switch (status) {
      case EventStatus.draft:
        return 'Draft';
      case EventStatus.active:
        return 'Active';
      case EventStatus.booked:
        return 'Booked';
      case EventStatus.inProgress:
        return 'In Progress';
      case EventStatus.completed:
        return 'Completed';
      case EventStatus.cancelled:
        return 'Cancelled';
    }
  }
}
