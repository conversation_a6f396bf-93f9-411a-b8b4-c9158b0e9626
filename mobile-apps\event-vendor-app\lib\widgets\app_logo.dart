import 'package:flutter/material.dart';
import '../config/app_theme.dart';

enum LogoVariant {
  auto, // Automatically choose based on background
  black, // Force black logo (for white/light backgrounds)
  white, // Force white logo (for colored/dark backgrounds)
}

class AppLogo extends StatelessWidget {
  final double size;
  final bool showBackground;
  final EdgeInsets? padding;
  final List<BoxShadow>? boxShadow;
  final LogoVariant variant;

  const AppLogo({
    super.key,
    this.size = 36,
    this.showBackground = true,
    this.padding,
    this.boxShadow,
    this.variant = LogoVariant.auto,
  });

  @override
  Widget build(BuildContext context) {
    final effectivePadding = padding ?? EdgeInsets.all(size * 0.15);

    // Determine which logo to use based on variant and background
    String logoPath = _getLogoPath();

    Widget logoWidget = Image.asset(
      logoPath,
      fit: BoxFit.contain,
      errorBuilder: (context, error, stackTrace) {
        // Debug: Print error details
        debugPrint('🚨 Logo loading failed: $error');
        debugPrint('🚨 Asset path: $logoPath');

        // Fallback to icon if asset fails to load
        return Container(
          decoration: BoxDecoration(
            color: Colors.red.withValues(alpha: 0.1),
            border: Border.all(color: Colors.red, width: 1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error,
                size: size * 0.3,
                color: Colors.red,
              ),
              Text(
                'Logo\nFailed',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: size * 0.1,
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        );
      },
    );

    if (showBackground) {
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: boxShadow,
        ),
        child: Padding(
          padding: effectivePadding,
          child: logoWidget,
        ),
      );
    }

    return SizedBox(
      width: size,
      height: size,
      child: Padding(
        padding: effectivePadding,
        child: logoWidget,
      ),
    );
  }

  /// Determines which logo to use based on the variant setting
  String _getLogoPath() {
    switch (variant) {
      case LogoVariant.black:
        return 'assets/logos/link_in_blink_black.png';
      case LogoVariant.white:
        return 'assets/logos/logo.png';
      case LogoVariant.auto:
        // Auto logic: if showBackground is true (white background), use black logo
        // Otherwise, assume it's on a colored background and use white logo
        return showBackground
            ? 'assets/logos/link_in_blink_black.png' // Black logo on white background
            : 'assets/logos/logo.png'; // White logo on colored background
    }
  }
}

// Predefined logo sizes for consistency
class AppLogoSizes {
  static const double header = 36;
  static const double login = 120;
  static const double splash = 140;
}
