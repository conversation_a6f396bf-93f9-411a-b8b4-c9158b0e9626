import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/hybrid_auth_service.dart';

/// Helper class for Firestore data recovery and cleanup operations
class FirestoreRecoveryHelper {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final HybridAuthService _authService = HybridAuthService();

  /// Check what data exists locally that could be used for recovery
  static Future<Map<String, dynamic>> checkLocalDataForRecovery() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final allKeys = prefs.getKeys();

      final localData = <String, dynamic>{};

      // Check for any vendor-related data in SharedPreferences
      for (final key in allKeys) {
        if (key.contains('vendor') ||
            key.contains('room') ||
            key.contains('guest') ||
            key.contains('booking')) {
          final value = prefs.getString(key);
          if (value != null) {
            localData[key] = value;
          }
        }
      }

      if (kDebugMode) {
        print('🔍 Local data found for potential recovery:');
        localData.forEach((key, value) {
          print(
              '   $key: ${value.toString().substring(0, value.toString().length > 100 ? 100 : value.toString().length)}...');
        });
      }

      return localData;
    } catch (e) {
      if (kDebugMode) print('❌ Error checking local data: $e');
      return {};
    }
  }

  /// Check current Firestore structure
  static Future<Map<String, dynamic>> checkFirestoreStructure() async {
    try {
      final result = <String, dynamic>{};

      // Check if vendors collection exists
      final vendorsSnapshot =
          await _firestore.collection('vendors').limit(1).get();
      result['vendorsCollectionExists'] = vendorsSnapshot.docs.isNotEmpty;
      result['vendorsCount'] = vendorsSnapshot.docs.length;

      // Check current user
      final currentUser = _authService.currentUser;
      result['currentUser'] = currentUser;

      if (currentUser != null) {
        final userId = currentUser['id'];

        // Check if current user's vendor document exists
        final userDoc =
            await _firestore.collection('vendors').doc(userId).get();
        result['userDocumentExists'] = userDoc.exists;

        if (userDoc.exists) {
          result['userDocumentData'] = userDoc.data();

          // Check subcollections
          final roomsSnapshot = await _firestore
              .collection('vendors')
              .doc(userId)
              .collection('rooms')
              .limit(1)
              .get();
          result['roomsSubcollectionExists'] = roomsSnapshot.docs.isNotEmpty;

          final guestsSnapshot = await _firestore
              .collection('vendors')
              .doc(userId)
              .collection('guests')
              .limit(1)
              .get();
          result['guestsSubcollectionExists'] = guestsSnapshot.docs.isNotEmpty;

          final bookingsSnapshot = await _firestore
              .collection('vendors')
              .doc(userId)
              .collection('bookings')
              .limit(1)
              .get();
          result['bookingsSubcollectionExists'] =
              bookingsSnapshot.docs.isNotEmpty;
        }
      }

      if (kDebugMode) {
        print('🔍 Firestore structure check:');
        result.forEach((key, value) {
          print('   $key: $value');
        });
      }

      return result;
    } catch (e) {
      if (kDebugMode) print('❌ Error checking Firestore structure: $e');
      return {'error': e.toString()};
    }
  }

  /// Clear ALL data from Firestore (use with extreme caution!)
  static Future<Map<String, dynamic>> clearAllFirestoreData() async {
    try {
      if (kDebugMode) {
        print('⚠️  WARNING: Starting complete Firestore cleanup...');
      }

      final result = <String, dynamic>{
        'deletedCollections': [],
        'deletedDocuments': 0,
        'errors': [],
      };

      // Get all vendors
      final vendorsSnapshot = await _firestore.collection('vendors').get();

      for (final vendorDoc in vendorsSnapshot.docs) {
        try {
          final vendorId = vendorDoc.id;

          // Delete all subcollections for this vendor
          await _deleteSubcollection(vendorId, 'rooms');
          await _deleteSubcollection(vendorId, 'guests');
          await _deleteSubcollection(vendorId, 'bookings');

          // Delete the vendor document itself
          await vendorDoc.reference.delete();
          result['deletedDocuments'] = (result['deletedDocuments'] as int) + 1;

          if (kDebugMode) {
            print('✅ Deleted vendor: $vendorId');
          }
        } catch (e) {
          result['errors'].add('Error deleting vendor ${vendorDoc.id}: $e');
        }
      }

      result['deletedCollections'].add('vendors');

      if (kDebugMode) {
        print('✅ Firestore cleanup completed');
        print('   Deleted ${result['deletedDocuments']} vendor documents');
        print('   Errors: ${result['errors'].length}');
      }

      return result;
    } catch (e) {
      if (kDebugMode) print('❌ Error during Firestore cleanup: $e');
      return {'error': e.toString()};
    }
  }

  /// Delete a subcollection for a vendor
  static Future<void> _deleteSubcollection(
      String vendorId, String subcollectionName) async {
    try {
      final subcollectionSnapshot = await _firestore
          .collection('vendors')
          .doc(vendorId)
          .collection(subcollectionName)
          .get();

      for (final doc in subcollectionSnapshot.docs) {
        await doc.reference.delete();
      }

      if (kDebugMode && subcollectionSnapshot.docs.isNotEmpty) {
        print(
            '   Deleted ${subcollectionSnapshot.docs.length} documents from $vendorId/$subcollectionName');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error deleting subcollection $subcollectionName: $e');
      }
    }
  }

  /// Clear local app data
  static Future<Map<String, dynamic>> clearLocalAppData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final allKeys = prefs.getKeys().toList();

      // Clear all SharedPreferences
      await prefs.clear();

      if (kDebugMode) {
        print('✅ Cleared ${allKeys.length} local storage keys');
      }

      return {
        'success': true,
        'clearedKeys': allKeys.length,
        'keys': allKeys,
      };
    } catch (e) {
      if (kDebugMode) print('❌ Error clearing local data: $e');
      return {'error': e.toString()};
    }
  }

  /// Create a fresh vendor document for current user
  static Future<Map<String, dynamic>> createFreshVendorDocument() async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        return {'error': 'No user logged in'};
      }

      final userId = currentUser['id'];
      final userEmail = currentUser['email'];

      // Create fresh vendor document
      await _firestore.collection('vendors').doc(userId).set({
        'email': userEmail,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'dataInitialized': true,
      });

      if (kDebugMode) {
        print('✅ Created fresh vendor document for user: $userId');
      }

      return {
        'success': true,
        'userId': userId,
        'email': userEmail,
      };
    } catch (e) {
      if (kDebugMode) print('❌ Error creating fresh vendor document: $e');
      return {'error': e.toString()};
    }
  }
}
