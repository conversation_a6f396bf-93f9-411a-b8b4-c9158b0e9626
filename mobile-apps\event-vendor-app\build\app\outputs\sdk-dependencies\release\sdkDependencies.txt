# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.1.0"
  }
  digests {
    sha256: "\326\371\033{\0170l\312)\237\354t\373|4\344\207Mo^\305\271%\240\264\336!\220\036\021\234?"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.22"
  }
  digests {
    sha256: "A\230\260\352\360\220\244\362[o~ZYX\037C\024\272\214\237l\321\321>\351\323H\346^\330\367\a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.8.22"
  }
  digests {
    sha256: "\005_\\\262B\207\372\020a\000\231Z{G\253\222\022k\201\3502\350u\365\372,\360\275Ui=\v"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.1.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "armeabi_v7a_release"
    version: "1.0.0-ef0cd000916d64fa0c5d09cc809fa7ad244a5767"
  }
  digests {
    sha256: "5\272I\021+\003;\b\201\205W#\214\225l\350\330d )NTe\002b\216?U\225`\023\032"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "arm64_v8a_release"
    version: "1.0.0-ef0cd000916d64fa0c5d09cc809fa7ad244a5767"
  }
  digests {
    sha256: "\212\204ba5\310H>\277s8.\322L\275\315\270\356\033q!\255:\224\236t\225BETY\264"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "x86_64_release"
    version: "1.0.0-ef0cd000916d64fa0c5d09cc809fa7ad244a5767"
  }
  digests {
    sha256: "\365@\r\330.T\363\023\366\036\303\364\023e\234\027;=\224E|\243\205\\}\000\262\a)\035?_"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-bom"
    version: "33.16.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-analytics"
    version: "22.5.0"
  }
  digests {
    sha256: "\\d|mO<\263\205c\306j\272Z\211\330\244h\002\275\030\343`3db6\241e3\f\3379"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement"
    version: "22.5.0"
  }
  digests {
    sha256: "R\306#\177\237.\201\017\022\361\003@\354\002\204o\270T\0234\023\214b\305\250\263\205.\0170nE"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.9.1"
  }
  digests {
    sha256: "\03649\027\353\362{\251o\344\334R\261\312\327\3752\2678\373\3065[\266\315[;0]r\022\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.13.1"
  }
  digests {
    sha256: ",\'\336\031\2255gP\005U0fYzK \372\036\352|\"\212\264\357k2\265\3769\312\037Y"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.0"
  }
  digests {
    sha256: "\306\353~g`\021\354e\263$(7=E\r\353\337\304Qy\304\370\263\247R\027O\270|\027\260\212"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.7.0"
  }
  digests {
    sha256: "\201\306\373\035\273j<\327\334\202}{\b\341\310\024.\323\244\000\243B$A\020\204\200\342/\2117\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.7.0"
  }
  digests {
    sha256: "S=\355\216\340dZ\030\366e=\245?\341\354Z\025C\016\1776\2477\324^A\0051\363\0173\030"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-play-services"
    version: "1.7.3"
  }
  digests {
    sha256: "d\326\352\032M\025\242\300\225\r\251\246\037I\352|Q&\216\347L;\035\335\304c\346\225TD\033$"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.2.0"
  }
  digests {
    sha256: "\177*\252\217P h\352\365CV\312\222\256\300Bq\326\347\304\026\305,E\300\3224@\374\275\026T"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.5.0"
  }
  digests {
    sha256: "\243\337n\373)\276\262\377x\373|\336q*\307s\275l\334\337\311\203<\220\030\225\200O`\234\231^"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.7.1"
  }
  digests {
    sha256: "}\336\276\235\203\n\004\22673\334\263@\312\264\272\301\346\235\374\247\227\246\325\232\247\274V(!P\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.10.1"
  }
  digests {
    sha256: "\266+R\214\221}\351\276I~\266\370\2100\031| \351\322\022g\303\221la4\222\345\356\203}M"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.13.1"
  }
  digests {
    sha256: "\031\272P\320\224\3076\216\336\033L\317\021\225\316\270>5\227\a6Y?\202>Z\367\026\370\320]p"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-viewtree"
    version: "1.0.0"
  }
  digests {
    sha256: "\334\033g\215X\353\317+\372\025\207\276h\377\202f\224\316=\"\022Q\271\3570\324\324\263b\227\346\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.7.0"
  }
  digests {
    sha256: "N\035\222\342\211\222\f\327\265\0166q\271\031\033\324\023@sI\315\246\366\002\260(Td\364\034\034\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.7.0"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.7.0"
  }
  digests {
    sha256: "o\263=\224s\244\223=\251\331\212\v\022\266\006\022~\200h\033\331\271\003\t\314\322\302#\bc\2719"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.7.0"
  }
  digests {
    sha256: "W\305x\206.!\2366\fiW\377\v\312o\026\227\a\261\345X-O\245\223\342\204\367\366>3\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.7.0"
  }
  digests {
    sha256: "\257\216\021\270~\003\rn1\a\3559\255\317\001\372\020\326%\236\261\\C\313\246\347\264\343\377\256\337\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\251\177L!\221\353\3352\371\232\302)Y{\321\251$F\260\034\321\240\210\3214\224\314\005\312\277\r\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\357p3\261]\262uiw\034<\n\353o\2229+VT!a\225\376t\023n\243\341\b\221*\324"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\264\222\333\374\205\222dq6q\022\366\320\345\322\311\231\036\205\\T!\25379\223\226\314\001#\342\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\002\024\037\314\351\002C\017b\266t\303*\354\320\367Rb\347\356,\321\\t\255\266\027\315\023\023\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.10.1"
  }
  digests {
    sha256: "\363\226\365\215\275w,\006[\2076\226\267J=M\buT\263vb\200\346;I\262]\273S\253\026"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.1.0"
  }
  digests {
    sha256: "\250;Y|\322@\235\301w\367\3712\367\304\b.\367\265P\213i\266\304\272q`~u\333\331\231q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment-ktx"
    version: "1.7.1"
  }
  digests {
    sha256: "\217\254\271\a\030\317R^\257\376m\033\326\024]\377\025mj\345\035<\037\202\002\315\005\216\370\201\366*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.1.0"
  }
  digests {
    sha256: "+\374TG\\\004q1\2213a\365m\017\177\001\234n[\356S\356\260\353}\224\247\304\231\240R\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.1.0"
  }
  digests {
    sha256: "\242+\224\247w\211\363\263K\354\322@\202#\023\000R@\021\314\310?\002aB\304\232\326\00117\233"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-identifier"
    version: "18.0.0"
  }
  digests {
    sha256: "s\f\233g\344\370]\2738I\364\363\344*PG\316\341\365\234#&F\235\001\331m\017\275!\030\032"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-base"
    version: "22.5.0"
  }
  digests {
    sha256: "Z\336\366Lc\316M.u2\327\313r\244\257\016j\nW4\t~\370@\260r\232\363\021\006\233L"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-impl"
    version: "22.5.0"
  }
  digests {
    sha256: "\270\201\314\214VT\341\320\336\324\332g\250_\262\355c\236\312\214=\242\n\242\312*\210E\342P\356\231"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices"
    version: "1.1.0-beta11"
  }
  digests {
    sha256: "T\nn-\307\2167\006\025\367\177\207,\017;p\n\032\312\035x\244\251\257}(\b\005\bH\306c"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices-java"
    version: "1.1.0-beta11"
  }
  digests {
    sha256: "\262\210]\265\335\255\233E\177\224\322\020A\332S&}\250\232\337\3448$+\240}\321\034\217\246>t"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.5.0"
  }
  digests {
    sha256: "Y\245\300\302\332\0221\035u\331e\316\037A\224\230Sk\032\026\177\262\217\367\337\302\337\331\316\372AW"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-stats"
    version: "17.0.2"
  }
  digests {
    sha256: "\335C\024\245?I\243x\354\024a\003\323b2\271luEM)Rc6\314\275\3612\224\027d\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "32.1.3-android"
  }
  digests {
    sha256: " \346\254\211\002\335\364\236x\006\314p\363\005L\215\221\254\313^\357\334\020\363 ~\200\340\2436\262c"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-qual"
    version: "3.37.0"
  }
  digests {
    sha256: "\344\316\023v\314\'5\341\335\342 \266*\320\221?Q)w\004\332\255\025Z3\363\206\274]\260\331\367"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.26.0"
  }
  digests {
    sha256: "S\315\374\v\353-vo\340;x\360\261\035\002\005T\315A\230y\322\003\200\303\217\241\334\362\272\033P"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk-api"
    version: "22.5.0"
  }
  digests {
    sha256: "\025\265\372\246\005\371\307\241W\220\315s\252h\0047[\200\324\226\026X\365\035\354L\245\252\365W\244\037"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-api"
    version: "22.5.0"
  }
  digests {
    sha256: "\024\373\037\273\\t\352q\207P\301\376\'O2R\3428RJ\260C\313\262{.\004\324mT\222\020"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common"
    version: "21.0.0"
  }
  digests {
    sha256: "7\222\207\327\027\023qQ$\223h\0339\216x\303A\2633\317\227N\350\032\030\261\325n|.8]"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-components"
    version: "18.0.0"
  }
  digests {
    sha256: "\307\304\212:\200\364JI\236\275ds\274\374}\325\244^\365#\372\276$\024\246%\025\377<d\tr"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-annotations"
    version: "16.2.0"
  }
  digests {
    sha256: "F\366\325\337\335,\317<@\336\211z\024\275\227y1L3\031\364K\3751\347\340\242\r\223Z^>"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common-ktx"
    version: "21.0.0"
  }
  digests {
    sha256: "%\374\200\311\273\236\313\026r\220\207\030\302\224\257\314J\301\344tsg{\340`\307\225\340O\022\000f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations"
    version: "18.0.0"
  }
  digests {
    sha256: "\225\006:\337\261\177\376I+\022\366\216s\002\bs\201M\201w\252U0i\312\326N\021\330\307\222\222"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations-interop"
    version: "17.1.1"
  }
  digests {
    sha256: "\372\306Ph\017y!\364\253\222\360\273!\240\2224\261,\332\357\253,\367\001\210(\035~\245+\215\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-measurement-connector"
    version: "19.0.0"
  }
  digests {
    sha256: "\333\247Mk\371FG\3569{\367\257\262\253\a\366\376\215\023\025~Vx_\245@\242\241>\330,\231"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk"
    version: "22.5.0"
  }
  digests {
    sha256: "\026\226\323\214\374\250\v\211\253\326n\315Au\327X\342\375lc\345\350\262\332+h\304\344\201|\n\\"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-auth"
    version: "23.2.1"
  }
  digests {
    sha256: "\300\177q\022\246b\f\225r@\241N\032\a\330[\340\001^@5d\245\3023\022\324\334N`\267\334"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.browser"
    artifactId: "browser"
    version: "1.8.0"
  }
  digests {
    sha256: "-ag\263;\366\240d\303u\001P\025Cp#I\350P\254b\3072\321\357\244\347\020cgTV"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.credentials"
    artifactId: "credentials"
    version: "1.2.0-rc01"
  }
  digests {
    sha256: "I\217e\033O\221f\277w\017l\251z\353\250\351!\356\301\001\263\337\370\331\360\305\032\310\366\344\002\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.credentials"
    artifactId: "credentials-play-services-auth"
    version: "1.2.0-rc01"
  }
  digests {
    sha256: "X0\246A8N\227\226A[0\363M\302Px-x\b\020\034\366\22264`\33199\240 \311"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth"
    version: "21.0.0"
  }
  digests {
    sha256: "\002\271\240\234N\f\261z\r\211<\277@\3741\323p\2262a\233\v\375%\036\\Q\343\347\256\327\273"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-api-phone"
    version: "18.0.2"
  }
  digests {
    sha256: "\277\3378\016t\000\251\331P\313\244\336\334\025+\033\316aJ\20750\t\350Q\bt(\222|\302@"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-base"
    version: "18.0.10"
  }
  digests {
    sha256: "\330\277\362\311\215#\2263\373U\002)\345\'w\224%\277\265\037\360K\226\254\017\022\323\254\272\v\037B"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-fido"
    version: "20.1.0"
  }
  digests {
    sha256: "\222r:8\344\r:\312\037\2365\021YR\231\220!\251/\254$\2371a\3552\223\2214r\251\270"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.libraries.identity.googleid"
    artifactId: "googleid"
    version: "1.1.0"
  }
  digests {
    sha256: "\031J\301\374\031\206\335\037b\004o\2567\335\367~cw\017\334\037=4\272\2529|\373\364\321\221\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "integrity"
    version: "1.3.0"
  }
  digests {
    sha256: "6\253\374C\227P\270%\355\333?W\a\347\357\002\034K8\354\341\334r\202f\205\256\273\327\000L3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "core-common"
    version: "2.0.3"
  }
  digests {
    sha256: "C\003%_\025,Y\271T\221\233\264q\321N\345\376\322\337x5e\336\250R\213\b\b;\2474+"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.recaptcha"
    artifactId: "recaptcha"
    version: "18.6.1"
  }
  digests {
    sha256: "]\r\\\350i\332c\004\2754\342\027\273\367e`\034Q\306\036\305\305\177/!\233\232\210\024\321\370\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-appcheck-interop"
    version: "17.1.0"
  }
  digests {
    sha256: "\036\241\020\250\3266\3042`\241\360}zE\372\005H\210Y\372\2637;\023\004/\201\022\005\266\376<"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-auth-interop"
    version: "20.0.0"
  }
  digests {
    sha256: "\300\237\332\337\240WI\315\177]h_\367\237<$\370\273\325+\241\310\033\216k\277M\000\230\301\312I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-firestore"
    version: "25.1.4"
  }
  digests {
    sha256: "F\021\333&\242\202\232=\300`w\2565\025\277\271\242\3544\000*\334~\201\025\211\222n\360F\375K"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "protolite-well-known-types"
    version: "18.0.1"
  }
  digests {
    sha256: "\355\327\206\202D\343\366x\226\357\260\304\306\274\233N\212\262W\v0\217E\312\376G\352\210x\225\346q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.protobuf"
    artifactId: "protobuf-javalite"
    version: "3.25.5"
  }
  digests {
    sha256: "y\243\377Q\254*\213\033\347\377\372\"\204\203\326It\361\177>\377\377\331f\260T\330qy\344V4"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-database-collection"
    version: "18.0.1"
  }
  digests {
    sha256: "\373\222`M\363[\370\031\347\006C/\366\343\312\235G\224\314\2054\215\224\310\207b+\251;TP\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-android"
    version: "1.62.2"
  }
  digests {
    sha256: "%D\222\016\245\364g \334\367^\202\000\243t\320\323v\375\310\362\224({\265yu\272\263\202\2469"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-api"
    version: "1.62.2"
  }
  digests {
    sha256: ".\211iD\317Q>\016\\\3752\274\327,\211`\032\'\306\312V\221o\204\262\017:\023\272\317\033\037"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-core"
    version: "1.62.2"
  }
  digests {
    sha256: "\030C\231\002\304s\242\301Q\036Q}\023\270\256ycx\205\n\216\332Cx|k\247x\372\220\374\305"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.10.1"
  }
  digests {
    sha256: "BA\301Jw\'\303O\356\246P~\310\0011\212=J\220\360p\344RV\201\a\237\271N\344\305\223"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android"
    artifactId: "annotations"
    version: "4.1.1.4"
  }
  digests {
    sha256: "\272sN\036\204\300\235aZ\366\240\2353\003KO\004B\370w-\354\022\016\3737m\206\245e\256\025"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.codehaus.mojo"
    artifactId: "animal-sniffer-annotations"
    version: "1.23"
  }
  digests {
    sha256: "\237\376Rk\364:cH\351\330\263;\234\326\365\200\247\365\356\320\317\005Y\023\000~\332&=\351t\320"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.perfmark"
    artifactId: "perfmark-api"
    version: "0.26.0"
  }
  digests {
    sha256: "\267\322>\223\243E7\3163\'\b&\232\r\024\004x\212[^\031I\350/U5\374\345\033>\251["
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-context"
    version: "1.62.2"
  }
  digests {
    sha256: "\231Yt}\366\247S\021\236\034\032=\377\001\252vm$U\365\344\206\n\312\243\0055\236\035S:\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-okhttp"
    version: "1.62.2"
  }
  digests {
    sha256: "\236\220?\375+0\322\373{T\312Mr\221[\032\263[\r\"\274\220\230V[\016\217B(\325\232\376"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-util"
    version: "1.62.2"
  }
  digests {
    sha256: "<q\003\346\363s\205q\343\256\332B\017\342\246\254h\343TSM\213f\364\030\227\266u[H\2675"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.4.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.4.0"
  }
  digests {
    sha256: "\0019\354zPm\273\325L\255b)\033\001\234\270PSK\340\227\310\306l\020\000\325\373\350\355\357>"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-protobuf-lite"
    version: "1.62.2"
  }
  digests {
    sha256: "y\231y\211\250\302\265\277M\321\201\202\242\337./f\207\003\326\213\247\303\027\347\240x\t\323?\221\364"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-stub"
    version: "1.62.2"
  }
  digests {
    sha256: "\373L\246y\244!AC@le\254Ag\262\265\342\356,\253\037\301\001Vk\261\304i]\020^6"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-messaging"
    version: "24.1.2"
  }
  digests {
    sha256: "\215X\177\1777$\325\303\311\022\251^\354\367+\001rTb1|\323\025m\322\"0\031V\224va"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-datatransport"
    version: "18.2.0"
  }
  digests {
    sha256: "\262\262\217k\241s\365\340\304\376=6\242\327\356R9\307\254\277AC\265I\354\3601\243H[5\273"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-api"
    version: "3.1.0"
  }
  digests {
    sha256: "}\257\303\237\016\2505G3f\254\303F\367\346\177\310D?Ld]\231\234>\230{\312\326\270\214{"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-runtime"
    version: "3.1.9"
  }
  digests {
    sha256: "At\\[\217B}$C\220\025\270Oe\032\324 q\211\221\206}*\374&,&@\t\270\002\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders"
    version: "17.0.0"
  }
  digests {
    sha256: "(*Zp?\233~\265e\b\335\351~\251\030\351]s1\213\025pP\364W\367\250m\312u\001P"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-proto"
    version: "16.0.0"
  }
  digests {
    sha256: ")=\271j\r\035C\3603\026x\201\2668\330\375\350D\344\345I_Q\001\317R)We)^\016"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-backend-cct"
    version: "3.1.9"
  }
  digests {
    sha256: "\a\243 %\246[\b\356~\021\321M\3059\247X\266g\023\360\301\3219\000\250\025\231\316\255\272\364M"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-json"
    version: "18.0.0"
  }
  digests {
    sha256: "\200\256\316~\036\365\211W\312/\301\225{\311 \216\311*:\225( \0231\323\306>1\202W\017\227"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-iid-interop"
    version: "17.1.0"
  }
  digests {
    sha256: "\v|7!\310Kb\347\004\0250r9\355J\177\231\211\211\bK\362\203?\220\271\365\276\243\tZ\005"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-cloud-messaging"
    version: "17.2.0"
  }
  digests {
    sha256: "\'%^\177\351pd\203\201k\025\215\262\\\363\031\366\242j\005f\376\377AY|\350\200z5\0167"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-storage"
    version: "21.0.2"
  }
  digests {
    sha256: ".K\200X\311&\246\rIZ\374\254\037\213\000Q\320\205\351\225\334\240S\244\344\340\236}\">~\322"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-appcheck"
    version: "18.0.0"
  }
  digests {
    sha256: "\322z\363\363\034\214\316\241\351\003\f\035\303\204\021\304\v\230g?\027~\3763\344\361\265\021\360\366Y\220"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "flutter_embedding_release"
    version: "1.0.0-ef0cd000916d64fa0c5d09cc809fa7ad244a5767"
  }
  digests {
    sha256: "\036X&\276r%\350\311\022\234@je!\273\017\214\362RR\377\3719\271p\363\236\276\244\")("
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window-java"
    version: "1.2.0"
  }
  digests {
    sha256: "\343\336\373^\343\205wWP]z\326bu\334:\v\001Ai0\033\211\326\344\356\300;\016E\274\253"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window"
    version: "1.2.0"
  }
  digests {
    sha256: "\2504\302\027\331s\021\'wS=\257\022\201\332\263;\327x\372\335-f\276\2749\265\376\201\250\220\254"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window.extensions.core"
    artifactId: "core"
    version: "1.0.0"
  }
  digests {
    sha256: "1\000\205K-\300\336\\M\210\264~\034]\263\b\001w\f\202\035\312(\030\224C\267bO@\b\256"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.getkeepsafe.relinker"
    artifactId: "relinker"
    version: "1.4.5"
  }
  digests {
    sha256: "\260;M\021:\237\357y@\n\350T\301wW\302\221W%\346\377\321\326\021\026\352\332\353V\0237J"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-location"
    version: "21.2.0"
  }
  digests {
    sha256: "\274\260mi\312\212D\263X8\216\f \345\017\341ZX\265\223\244\313\231\310\315$\366\340\021[\252\326"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.7"
  }
  digests {
    sha256: "\016\217\0302&l[\006g\255=;\020\230\346$\344\232\t\aT\223\240\024\247\350\212\360\037\323\n\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-android"
    version: "1.1.3"
  }
  digests {
    sha256: "z\235o`!\215w_\3549\250X@C5\235\373jcZa\326\206\027\252n\230&\330\232\001;"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core-android"
    version: "1.1.3"
  }
  digests {
    sha256: "\260\257x\371\b\203@z\346?\321HW\304\346.w\037>\236?\202\340\227q[M\2518\215\340\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-parcelize-runtime"
    version: "1.9.22"
  }
  digests {
    sha256: "\360\a\1775\354\202\216\024\212\311-\fj\241A\2472\265\353zJ9\026y\256\357\245\237\251\241\245\314"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-android-extensions-runtime"
    version: "1.9.22"
  }
  digests {
    sha256: "\360\0057\307\002>`I3\366\251V;\033\263\270>a\373\375,\303\320\351\220\rak\253hb\244"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core-okio"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core-okio-jvm"
    version: "1.1.3"
  }
  digests {
    sha256: ")\025\006\264\374\343\230$\227\223\343\' v5\275M\362Q\201\331S\230\b\327\030\032J\275\bu\365"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-android"
    version: "1.1.3"
  }
  digests {
    sha256: "\220\333\\\004\261Ho\255*\017\342\220 \3769D\300\372\f]6\361A\036\336\241\231\000t*E\023"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-core"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-core-jvm"
    version: "1.1.3"
  }
  digests {
    sha256: "\3139\341\";C$\3270\305s\242\274\352:50\236\275\223X\373\3460\211\227\005\243\373\312\312\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-proto"
    version: "1.1.3"
  }
  digests {
    sha256: "chja/9\270\312`;\353\3437mU\373l\236\342\212JQs\357\244\234\211\313\314\250\241\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-external-protobuf"
    version: "1.1.3"
  }
  digests {
    sha256: "\374\263\363s\317t4&\310^\273\r<\306D\256\372\362o9\267\372U\273\220#\215A\266\211\354\247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.preference"
    artifactId: "preference"
    version: "1.2.1"
  }
  digests {
    sha256: "@\312\212\337\333~\377\266\037\254\263\233\331\312./:@\321\006t;\f\326\334\236!\350\276\335O\205"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.1.0"
  }
  digests {
    sha256: "\215r\231\274\244L\263\275\361\177U\225vj\313\364Y\374\201\376\342#\350hl\306\254\323\244*\265\300"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.1.0"
  }
  digests {
    sha256: "\031\224M2\264eQ\241|4~!\211K\225\203\177\275{\252\257\311\342\b\'\2244O\"/sa"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\224\002D,\334ZC\317b\373\024\370\317\230\3063B\324\331\331\270\005\310\003<l\367\350\002t\232\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.0.0"
  }
  digests {
    sha256: "\006\225o\261\254\001@\'\312\235+@F\232KB\252a\264\225{\261\030H\341\3775\'\001\253EH"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-ui"
    version: "1.0.0"
  }
  digests {
    sha256: "\r\022`\306\347\346\2437\370u\337q\265\026\223\036p?qn\220\210\230\027\315: \372Z\303\331G"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\345\b\306\225H\224\2237M\224+\367\264\356\002\253\367W\035%\252\304\306\"\345}l\325\315)\353s"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.2.0"
  }
  digests {
    sha256: "_S3\233\342\244\371\n\232\276\243W\035\325\236p\250\244\236\177\025\335\202\227J8\230\264e.\207\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.4.1"
  }
  digests {
    sha256: "6\322\215\236\303:\214d18B\274\351\234\225sm\245\262zk:Q69\005\r\350/\aW&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\227a\263\250\t\311\260\223\375\006\243\304\273\306Eum\354\016\225\265\311\332A\233\311\362\243\363\002n\215"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.asynclayoutinflater"
    artifactId: "asynclayoutinflater"
    version: "1.0.0"
  }
  digests {
    sha256: "\367\352\266\fW\255\335\224\273\006\'X2\376v\000a\033\352\252\341\241\354Y|#\031V\372\371l\213"
  }
  repo_index {
  }
}
library {
  digests {
    sha256: "} \227\264pp\354\0211\230=\235Kt\312 \221L^\202\350M\307\n%\333(\231\326!\031\301"
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 3
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
}
library_dependencies {
  library_index: 4
  library_dep_index: 0
}
library_dependencies {
  library_index: 8
  library_dep_index: 9
  library_dep_index: 81
  library_dep_index: 72
  library_dep_index: 95
  library_dep_index: 113
  library_dep_index: 123
  library_dep_index: 76
  library_dep_index: 117
  library_dep_index: 77
  library_dep_index: 124
}
library_dependencies {
  library_index: 9
  library_dep_index: 10
  library_dep_index: 71
  library_dep_index: 80
}
library_dependencies {
  library_index: 10
  library_dep_index: 11
  library_dep_index: 14
  library_dep_index: 58
  library_dep_index: 30
  library_dep_index: 59
  library_dep_index: 60
  library_dep_index: 70
  library_dep_index: 64
}
library_dependencies {
  library_index: 11
  library_dep_index: 12
}
library_dependencies {
  library_index: 12
  library_dep_index: 13
}
library_dependencies {
  library_index: 13
  library_dep_index: 0
}
library_dependencies {
  library_index: 14
  library_dep_index: 12
  library_dep_index: 15
  library_dep_index: 55
  library_dep_index: 49
  library_dep_index: 56
  library_dep_index: 57
}
library_dependencies {
  library_index: 15
  library_dep_index: 12
  library_dep_index: 16
  library_dep_index: 11
  library_dep_index: 17
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 54
  library_dep_index: 0
  library_dep_index: 33
}
library_dependencies {
  library_index: 16
  library_dep_index: 0
}
library_dependencies {
  library_index: 17
  library_dep_index: 12
  library_dep_index: 18
}
library_dependencies {
  library_index: 19
  library_dep_index: 12
}
library_dependencies {
  library_index: 20
  library_dep_index: 12
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 47
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 35
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 42
}
library_dependencies {
  library_index: 21
  library_dep_index: 12
}
library_dependencies {
  library_index: 22
  library_dep_index: 12
  library_dep_index: 21
}
library_dependencies {
  library_index: 23
  library_dep_index: 12
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 20
  library_dep_index: 35
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 42
}
library_dependencies {
  library_index: 24
  library_dep_index: 25
  library_dep_index: 27
  library_dep_index: 2
}
library_dependencies {
  library_index: 25
  library_dep_index: 26
}
library_dependencies {
  library_index: 26
  library_dep_index: 1
  library_dep_index: 27
  library_dep_index: 2
}
library_dependencies {
  library_index: 27
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 25
  library_dep_index: 28
}
library_dependencies {
  library_index: 28
  library_dep_index: 25
  library_dep_index: 27
  library_dep_index: 29
  library_dep_index: 2
}
library_dependencies {
  library_index: 29
  library_dep_index: 30
}
library_dependencies {
  library_index: 30
  library_dep_index: 11
  library_dep_index: 15
  library_dep_index: 31
}
library_dependencies {
  library_index: 31
  library_dep_index: 32
  library_dep_index: 12
  library_dep_index: 16
  library_dep_index: 11
  library_dep_index: 33
  library_dep_index: 41
  library_dep_index: 20
  library_dep_index: 35
  library_dep_index: 40
  library_dep_index: 49
  library_dep_index: 47
  library_dep_index: 45
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 52
}
library_dependencies {
  library_index: 32
  library_dep_index: 12
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 23
  library_dep_index: 20
  library_dep_index: 35
  library_dep_index: 40
  library_dep_index: 47
  library_dep_index: 45
  library_dep_index: 39
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 0
  library_dep_index: 48
}
library_dependencies {
  library_index: 33
  library_dep_index: 12
  library_dep_index: 15
  library_dep_index: 0
  library_dep_index: 15
}
library_dependencies {
  library_index: 34
  library_dep_index: 0
  library_dep_index: 0
}
library_dependencies {
  library_index: 35
  library_dep_index: 12
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 36
  library_dep_index: 41
  library_dep_index: 37
  library_dep_index: 20
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 40
  library_dep_index: 42
}
library_dependencies {
  library_index: 36
  library_dep_index: 12
  library_dep_index: 23
  library_dep_index: 23
  library_dep_index: 37
  library_dep_index: 20
  library_dep_index: 35
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 42
}
library_dependencies {
  library_index: 37
  library_dep_index: 12
  library_dep_index: 20
  library_dep_index: 38
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 36
  library_dep_index: 20
  library_dep_index: 35
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 42
}
library_dependencies {
  library_index: 38
  library_dep_index: 12
  library_dep_index: 39
}
library_dependencies {
  library_index: 39
  library_dep_index: 12
}
library_dependencies {
  library_index: 40
  library_dep_index: 12
  library_dep_index: 33
  library_dep_index: 41
  library_dep_index: 35
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 23
  library_dep_index: 36
  library_dep_index: 41
  library_dep_index: 37
  library_dep_index: 20
  library_dep_index: 43
  library_dep_index: 35
  library_dep_index: 44
  library_dep_index: 42
}
library_dependencies {
  library_index: 41
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 36
  library_dep_index: 42
  library_dep_index: 37
  library_dep_index: 20
  library_dep_index: 43
  library_dep_index: 35
  library_dep_index: 44
  library_dep_index: 40
}
library_dependencies {
  library_index: 42
  library_dep_index: 41
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 36
  library_dep_index: 41
  library_dep_index: 37
  library_dep_index: 20
  library_dep_index: 43
  library_dep_index: 35
  library_dep_index: 44
  library_dep_index: 40
}
library_dependencies {
  library_index: 43
  library_dep_index: 12
  library_dep_index: 20
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 23
  library_dep_index: 36
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 37
  library_dep_index: 20
  library_dep_index: 35
  library_dep_index: 44
  library_dep_index: 40
}
library_dependencies {
  library_index: 44
  library_dep_index: 35
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 23
  library_dep_index: 36
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 37
  library_dep_index: 20
  library_dep_index: 43
  library_dep_index: 35
  library_dep_index: 40
}
library_dependencies {
  library_index: 45
  library_dep_index: 12
  library_dep_index: 21
  library_dep_index: 23
  library_dep_index: 0
  library_dep_index: 46
}
library_dependencies {
  library_index: 46
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 45
}
library_dependencies {
  library_index: 47
  library_dep_index: 12
  library_dep_index: 17
  library_dep_index: 38
  library_dep_index: 18
}
library_dependencies {
  library_index: 48
  library_dep_index: 32
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 46
  library_dep_index: 32
}
library_dependencies {
  library_index: 49
  library_dep_index: 12
  library_dep_index: 15
  library_dep_index: 41
  library_dep_index: 35
  library_dep_index: 11
}
library_dependencies {
  library_index: 50
  library_dep_index: 12
  library_dep_index: 15
  library_dep_index: 51
}
library_dependencies {
  library_index: 51
  library_dep_index: 12
  library_dep_index: 15
  library_dep_index: 11
}
library_dependencies {
  library_index: 52
  library_dep_index: 48
  library_dep_index: 53
  library_dep_index: 33
  library_dep_index: 31
  library_dep_index: 42
  library_dep_index: 44
  library_dep_index: 46
  library_dep_index: 0
  library_dep_index: 31
}
library_dependencies {
  library_index: 53
  library_dep_index: 0
  library_dep_index: 11
}
library_dependencies {
  library_index: 54
  library_dep_index: 12
  library_dep_index: 11
}
library_dependencies {
  library_index: 55
  library_dep_index: 12
}
library_dependencies {
  library_index: 56
  library_dep_index: 12
}
library_dependencies {
  library_index: 57
  library_dep_index: 12
}
library_dependencies {
  library_index: 58
  library_dep_index: 30
}
library_dependencies {
  library_index: 59
  library_dep_index: 30
}
library_dependencies {
  library_index: 60
  library_dep_index: 11
  library_dep_index: 15
  library_dep_index: 61
  library_dep_index: 62
  library_dep_index: 58
  library_dep_index: 63
  library_dep_index: 30
  library_dep_index: 59
  library_dep_index: 64
  library_dep_index: 29
  library_dep_index: 65
}
library_dependencies {
  library_index: 61
  library_dep_index: 12
  library_dep_index: 33
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 62
}
library_dependencies {
  library_index: 62
  library_dep_index: 12
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 61
  library_dep_index: 18
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 61
}
library_dependencies {
  library_index: 63
  library_dep_index: 11
  library_dep_index: 15
  library_dep_index: 31
  library_dep_index: 30
  library_dep_index: 29
}
library_dependencies {
  library_index: 64
  library_dep_index: 14
  library_dep_index: 30
}
library_dependencies {
  library_index: 65
  library_dep_index: 66
  library_dep_index: 18
  library_dep_index: 67
  library_dep_index: 68
  library_dep_index: 69
}
library_dependencies {
  library_index: 70
  library_dep_index: 30
  library_dep_index: 59
}
library_dependencies {
  library_index: 71
  library_dep_index: 58
  library_dep_index: 30
  library_dep_index: 59
  library_dep_index: 70
  library_dep_index: 29
  library_dep_index: 72
  library_dep_index: 76
  library_dep_index: 73
  library_dep_index: 77
  library_dep_index: 78
  library_dep_index: 79
  library_dep_index: 65
  library_dep_index: 0
}
library_dependencies {
  library_index: 72
  library_dep_index: 28
  library_dep_index: 73
  library_dep_index: 74
  library_dep_index: 12
  library_dep_index: 17
  library_dep_index: 0
  library_dep_index: 30
  library_dep_index: 29
}
library_dependencies {
  library_index: 73
  library_dep_index: 74
  library_dep_index: 12
  library_dep_index: 69
}
library_dependencies {
  library_index: 74
  library_dep_index: 75
}
library_dependencies {
  library_index: 76
  library_dep_index: 72
  library_dep_index: 2
  library_dep_index: 73
  library_dep_index: 74
}
library_dependencies {
  library_index: 77
  library_dep_index: 29
  library_dep_index: 74
  library_dep_index: 72
  library_dep_index: 76
  library_dep_index: 73
  library_dep_index: 78
  library_dep_index: 0
}
library_dependencies {
  library_index: 78
  library_dep_index: 29
  library_dep_index: 74
}
library_dependencies {
  library_index: 79
  library_dep_index: 30
  library_dep_index: 74
}
library_dependencies {
  library_index: 80
  library_dep_index: 11
  library_dep_index: 30
  library_dep_index: 59
  library_dep_index: 60
}
library_dependencies {
  library_index: 81
  library_dep_index: 82
  library_dep_index: 11
  library_dep_index: 83
  library_dep_index: 84
  library_dep_index: 31
  library_dep_index: 56
  library_dep_index: 86
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 90
  library_dep_index: 92
  library_dep_index: 74
  library_dep_index: 93
  library_dep_index: 94
  library_dep_index: 72
  library_dep_index: 76
  library_dep_index: 73
  library_dep_index: 0
}
library_dependencies {
  library_index: 82
  library_dep_index: 12
  library_dep_index: 16
  library_dep_index: 11
  library_dep_index: 17
  library_dep_index: 15
  library_dep_index: 19
  library_dep_index: 18
}
library_dependencies {
  library_index: 83
  library_dep_index: 12
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 84
}
library_dependencies {
  library_index: 84
  library_dep_index: 83
  library_dep_index: 85
  library_dep_index: 88
  library_dep_index: 89
  library_dep_index: 0
  library_dep_index: 83
}
library_dependencies {
  library_index: 85
  library_dep_index: 31
  library_dep_index: 49
  library_dep_index: 86
  library_dep_index: 87
  library_dep_index: 63
  library_dep_index: 30
  library_dep_index: 88
  library_dep_index: 29
}
library_dependencies {
  library_index: 86
  library_dep_index: 63
  library_dep_index: 30
  library_dep_index: 29
}
library_dependencies {
  library_index: 87
  library_dep_index: 11
  library_dep_index: 63
  library_dep_index: 30
  library_dep_index: 29
}
library_dependencies {
  library_index: 88
  library_dep_index: 63
  library_dep_index: 30
  library_dep_index: 29
}
library_dependencies {
  library_index: 89
  library_dep_index: 0
  library_dep_index: 2
}
library_dependencies {
  library_index: 90
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 91
}
library_dependencies {
  library_index: 92
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 90
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 28
}
library_dependencies {
  library_index: 93
  library_dep_index: 63
  library_dep_index: 29
}
library_dependencies {
  library_index: 94
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 74
  library_dep_index: 72
}
library_dependencies {
  library_index: 95
  library_dep_index: 96
  library_dep_index: 29
  library_dep_index: 74
  library_dep_index: 93
  library_dep_index: 94
  library_dep_index: 72
  library_dep_index: 76
  library_dep_index: 73
  library_dep_index: 98
  library_dep_index: 12
  library_dep_index: 63
  library_dep_index: 30
  library_dep_index: 99
  library_dep_index: 107
  library_dep_index: 111
  library_dep_index: 112
  library_dep_index: 0
  library_dep_index: 25
}
library_dependencies {
  library_index: 96
  library_dep_index: 97
}
library_dependencies {
  library_index: 98
  library_dep_index: 63
}
library_dependencies {
  library_index: 99
  library_dep_index: 100
  library_dep_index: 101
  library_dep_index: 65
}
library_dependencies {
  library_index: 100
  library_dep_index: 67
  library_dep_index: 69
  library_dep_index: 65
}
library_dependencies {
  library_index: 101
  library_dep_index: 100
  library_dep_index: 102
  library_dep_index: 103
  library_dep_index: 104
  library_dep_index: 69
  library_dep_index: 65
  library_dep_index: 105
  library_dep_index: 106
}
library_dependencies {
  library_index: 106
  library_dep_index: 100
}
library_dependencies {
  library_index: 107
  library_dep_index: 100
  library_dep_index: 108
  library_dep_index: 101
  library_dep_index: 109
  library_dep_index: 65
  library_dep_index: 105
}
library_dependencies {
  library_index: 108
  library_dep_index: 100
  library_dep_index: 101
  library_dep_index: 104
  library_dep_index: 65
}
library_dependencies {
  library_index: 109
  library_dep_index: 110
}
library_dependencies {
  library_index: 110
  library_dep_index: 2
  library_dep_index: 4
}
library_dependencies {
  library_index: 111
  library_dep_index: 100
  library_dep_index: 97
  library_dep_index: 67
  library_dep_index: 65
}
library_dependencies {
  library_index: 112
  library_dep_index: 100
  library_dep_index: 65
  library_dep_index: 69
}
library_dependencies {
  library_index: 113
  library_dep_index: 72
  library_dep_index: 76
  library_dep_index: 73
  library_dep_index: 114
  library_dep_index: 117
  library_dep_index: 120
  library_dep_index: 118
  library_dep_index: 121
  library_dep_index: 77
  library_dep_index: 78
  library_dep_index: 79
  library_dep_index: 12
  library_dep_index: 115
  library_dep_index: 119
  library_dep_index: 116
  library_dep_index: 63
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 122
  library_dep_index: 64
  library_dep_index: 69
  library_dep_index: 0
}
library_dependencies {
  library_index: 114
  library_dep_index: 115
  library_dep_index: 116
  library_dep_index: 119
  library_dep_index: 12
}
library_dependencies {
  library_index: 115
  library_dep_index: 12
}
library_dependencies {
  library_index: 116
  library_dep_index: 115
  library_dep_index: 12
  library_dep_index: 75
  library_dep_index: 117
  library_dep_index: 118
}
library_dependencies {
  library_index: 117
  library_dep_index: 12
}
library_dependencies {
  library_index: 118
  library_dep_index: 12
  library_dep_index: 117
}
library_dependencies {
  library_index: 119
  library_dep_index: 115
  library_dep_index: 116
  library_dep_index: 117
  library_dep_index: 120
  library_dep_index: 12
}
library_dependencies {
  library_index: 120
  library_dep_index: 12
  library_dep_index: 117
}
library_dependencies {
  library_index: 121
  library_dep_index: 30
  library_dep_index: 29
}
library_dependencies {
  library_index: 122
  library_dep_index: 30
  library_dep_index: 29
}
library_dependencies {
  library_index: 123
  library_dep_index: 74
  library_dep_index: 124
  library_dep_index: 93
  library_dep_index: 94
  library_dep_index: 72
  library_dep_index: 76
  library_dep_index: 73
  library_dep_index: 12
  library_dep_index: 63
  library_dep_index: 29
  library_dep_index: 0
  library_dep_index: 25
}
library_dependencies {
  library_index: 124
  library_dep_index: 29
  library_dep_index: 93
  library_dep_index: 76
  library_dep_index: 12
  library_dep_index: 63
  library_dep_index: 0
}
library_dependencies {
  library_index: 125
  library_dep_index: 23
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 20
  library_dep_index: 31
  library_dep_index: 12
  library_dep_index: 39
  library_dep_index: 15
  library_dep_index: 126
  library_dep_index: 129
}
library_dependencies {
  library_index: 126
  library_dep_index: 15
  library_dep_index: 127
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 127
}
library_dependencies {
  library_index: 127
  library_dep_index: 12
  library_dep_index: 11
  library_dep_index: 15
  library_dep_index: 128
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 126
}
library_dependencies {
  library_index: 128
  library_dep_index: 12
  library_dep_index: 0
}
library_dependencies {
  library_index: 130
  library_dep_index: 63
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 25
}
library_dependencies {
  library_index: 131
  library_dep_index: 12
}
library_dependencies {
  library_index: 132
  library_dep_index: 133
}
library_dependencies {
  library_index: 133
  library_dep_index: 12
  library_dep_index: 134
  library_dep_index: 138
  library_dep_index: 109
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 134
  library_dep_index: 138
  library_dep_index: 140
  library_dep_index: 142
  library_dep_index: 144
  library_dep_index: 145
}
library_dependencies {
  library_index: 134
  library_dep_index: 135
}
library_dependencies {
  library_index: 135
  library_dep_index: 12
  library_dep_index: 136
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 132
  library_dep_index: 138
  library_dep_index: 140
  library_dep_index: 142
  library_dep_index: 144
  library_dep_index: 145
}
library_dependencies {
  library_index: 136
  library_dep_index: 0
  library_dep_index: 137
}
library_dependencies {
  library_index: 137
  library_dep_index: 0
}
library_dependencies {
  library_index: 138
  library_dep_index: 139
}
library_dependencies {
  library_index: 139
  library_dep_index: 134
  library_dep_index: 109
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 132
  library_dep_index: 134
  library_dep_index: 140
  library_dep_index: 142
  library_dep_index: 144
  library_dep_index: 145
}
library_dependencies {
  library_index: 140
  library_dep_index: 141
}
library_dependencies {
  library_index: 141
  library_dep_index: 132
  library_dep_index: 142
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 132
  library_dep_index: 134
  library_dep_index: 138
  library_dep_index: 142
  library_dep_index: 144
  library_dep_index: 145
}
library_dependencies {
  library_index: 142
  library_dep_index: 143
}
library_dependencies {
  library_index: 143
  library_dep_index: 134
  library_dep_index: 138
  library_dep_index: 144
  library_dep_index: 109
  library_dep_index: 0
  library_dep_index: 132
  library_dep_index: 134
  library_dep_index: 138
  library_dep_index: 140
  library_dep_index: 144
  library_dep_index: 145
}
library_dependencies {
  library_index: 144
  library_dep_index: 145
  library_dep_index: 132
  library_dep_index: 134
  library_dep_index: 138
  library_dep_index: 140
  library_dep_index: 142
  library_dep_index: 145
}
library_dependencies {
  library_index: 145
  library_dep_index: 132
  library_dep_index: 134
  library_dep_index: 138
  library_dep_index: 140
  library_dep_index: 142
  library_dep_index: 144
}
library_dependencies {
  library_index: 146
  library_dep_index: 12
  library_dep_index: 147
  library_dep_index: 15
  library_dep_index: 48
  library_dep_index: 52
  library_dep_index: 153
  library_dep_index: 156
  library_dep_index: 11
}
library_dependencies {
  library_index: 147
  library_dep_index: 12
  library_dep_index: 15
  library_dep_index: 148
  library_dep_index: 31
  library_dep_index: 149
  library_dep_index: 152
  library_dep_index: 11
}
library_dependencies {
  library_index: 148
  library_dep_index: 12
}
library_dependencies {
  library_index: 149
  library_dep_index: 12
  library_dep_index: 15
  library_dep_index: 150
  library_dep_index: 151
  library_dep_index: 11
}
library_dependencies {
  library_index: 150
  library_dep_index: 12
  library_dep_index: 15
  library_dep_index: 11
}
library_dependencies {
  library_index: 151
  library_dep_index: 150
  library_dep_index: 19
  library_dep_index: 11
}
library_dependencies {
  library_index: 152
  library_dep_index: 12
  library_dep_index: 15
  library_dep_index: 51
}
library_dependencies {
  library_index: 153
  library_dep_index: 12
  library_dep_index: 15
  library_dep_index: 154
}
library_dependencies {
  library_index: 154
  library_dep_index: 12
  library_dep_index: 15
  library_dep_index: 14
  library_dep_index: 51
  library_dep_index: 50
  library_dep_index: 155
  library_dep_index: 152
  library_dep_index: 156
  library_dep_index: 19
  library_dep_index: 158
  library_dep_index: 159
  library_dep_index: 148
}
library_dependencies {
  library_index: 155
  library_dep_index: 12
  library_dep_index: 15
  library_dep_index: 51
}
library_dependencies {
  library_index: 156
  library_dep_index: 12
  library_dep_index: 51
  library_dep_index: 15
  library_dep_index: 127
  library_dep_index: 157
}
library_dependencies {
  library_index: 157
  library_dep_index: 12
  library_dep_index: 15
  library_dep_index: 11
}
library_dependencies {
  library_index: 158
  library_dep_index: 12
  library_dep_index: 15
  library_dep_index: 19
}
library_dependencies {
  library_index: 159
  library_dep_index: 12
  library_dep_index: 15
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 6
  dependency_index: 7
  dependency_index: 8
  dependency_index: 95
  dependency_index: 125
  dependency_index: 72
  dependency_index: 12
  dependency_index: 15
  dependency_index: 20
  dependency_index: 9
  dependency_index: 81
  dependency_index: 113
  dependency_index: 56
  dependency_index: 123
  dependency_index: 130
  dependency_index: 85
  dependency_index: 131
  dependency_index: 32
  dependency_index: 132
  dependency_index: 140
  dependency_index: 146
  dependency_index: 82
  dependency_index: 160
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://storage.googleapis.com/download.flutter.io"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
