import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../config/app_theme.dart';
import '../../models/event_model.dart';
import '../../services/event_service.dart';
import '../../widgets/app_logo.dart';
import '../../providers/vendor_auth_provider.dart';
import 'add_event_screen.dart';
import 'event_detail_screen.dart';

class EventsScreen extends StatefulWidget {
  const EventsScreen({super.key});

  @override
  State<EventsScreen> createState() => _EventsScreenState();
}

class _EventsScreenState extends State<EventsScreen>
    with TickerProviderStateMixin {
  final EventService _eventService = EventService();
  final TextEditingController _searchController = TextEditingController();

  List<EventModel> _events = [];
  List<EventModel> _filteredEvents = [];
  bool _isLoading = true;
  String _selectedFilter = 'All';
  late TabController _tabController;

  final List<String> _filterOptions = [
    'All',
    'Active',
    'Booked',
    'Draft',
    'Completed'
  ];

  @override
  void initState() {
    super.initState();
    _tabController =
        TabController(length: EventCategory.values.length, vsync: this);
    _loadEvents();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadEvents() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final vendorAuth =
          Provider.of<VendorAuthProvider>(context, listen: false);
      final vendorId = vendorAuth.currentVendor?.uid;

      if (vendorId != null) {
        final events = await _eventService.getVendorEvents(vendorId);
        setState(() {
          _events = events;
          _filteredEvents = events;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading events: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _filterEvents() {
    setState(() {
      _filteredEvents = _events.where((event) {
        // Apply search filter
        final matchesSearch = _searchController.text.isEmpty ||
            event.title
                .toLowerCase()
                .contains(_searchController.text.toLowerCase()) ||
            event.description
                .toLowerCase()
                .contains(_searchController.text.toLowerCase());

        // Apply status filter
        final matchesStatus = _selectedFilter == 'All' ||
            (_selectedFilter == 'Active' &&
                event.status == EventStatus.active) ||
            (_selectedFilter == 'Booked' && event.isBooked) ||
            (_selectedFilter == 'Draft' && event.status == EventStatus.draft) ||
            (_selectedFilter == 'Completed' && event.isCompleted);

        return matchesSearch && matchesStatus;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Transform.translate(
              offset: const Offset(-8, 0), // Move logo 8px to the left
              child: const AppLogo(
                size: AppLogoSizes.header,
                variant: LogoVariant.white,
                showBackground: false,
              ),
            ),
            const SizedBox(width: 4), // Reduced from 12 to 4
            const Text('Events'),
          ],
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          indicatorWeight: 3,
          labelPadding: const EdgeInsets.symmetric(horizontal: 16),
          tabs: EventCategory.values.map((category) {
            return Tab(text: _getCategoryDisplayName(category));
          }).toList(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _navigateToAddEvent(),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingM),
            child: Column(
              children: [
                // Search Bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search events...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppTheme.radiusM),
                    ),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              _filterEvents();
                            },
                          )
                        : null,
                  ),
                  onChanged: (value) => _filterEvents(),
                ),
                const SizedBox(height: AppTheme.spacingM),

                // Filter Chips
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: _filterOptions.map((filter) {
                      final isSelected = _selectedFilter == filter;
                      return Padding(
                        padding:
                            const EdgeInsets.only(right: AppTheme.spacingS),
                        child: FilterChip(
                          label: Text(filter),
                          selected: isSelected,
                          onSelected: (selected) {
                            setState(() {
                              _selectedFilter = filter;
                            });
                            _filterEvents();
                          },
                          backgroundColor:
                              isSelected ? AppTheme.primaryColor : null,
                          selectedColor: AppTheme.primaryColor,
                          labelStyle: TextStyle(
                            color: isSelected ? Colors.white : null,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
          ),

          // Events List
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: EventCategory.values.map((category) {
                return _buildEventsList(category);
              }).toList(),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToAddEvent(),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEventsList(EventCategory category) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    final categoryEvents =
        _filteredEvents.where((event) => event.category == category).toList();

    if (categoryEvents.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.event_busy,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: AppTheme.spacingM),
            Text(
              'No ${_getCategoryDisplayName(category).toLowerCase()} events found',
              style: AppTheme.bodyLarge.copyWith(color: Colors.grey[600]),
            ),
            const SizedBox(height: AppTheme.spacingM),
            ElevatedButton.icon(
              onPressed: () => _navigateToAddEvent(category: category),
              icon: const Icon(Icons.add),
              label: const Text('Create Event'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadEvents,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppTheme.spacingM),
        itemCount: categoryEvents.length,
        itemBuilder: (context, index) {
          final event = categoryEvents[index];
          return _buildEventCard(event);
        },
      ),
    );
  }

  Widget _buildEventCard(EventModel event) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingM),
      child: InkWell(
        onTap: () => _navigateToEventDetail(event),
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Event Header
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          event.title,
                          style: AppTheme.headingSmall,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: AppTheme.spacingXS),
                        Text(
                          event.categoryDisplayName,
                          style: AppTheme.bodyMedium.copyWith(
                            color: AppTheme.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildStatusChip(event.status),
                ],
              ),

              const SizedBox(height: AppTheme.spacingM),

              // Event Description
              Text(
                event.description,
                style: AppTheme.bodyMedium,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: AppTheme.spacingM),

              // Event Details
              Row(
                children: [
                  Icon(Icons.people, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: AppTheme.spacingXS),
                  Text(
                    '${event.estimatedGuests} guests',
                    style: AppTheme.bodySmall,
                  ),
                  const SizedBox(width: AppTheme.spacingM),
                  Icon(Icons.currency_rupee, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: AppTheme.spacingXS),
                  Text(
                    '₹${event.basePrice.toStringAsFixed(0)}',
                    style: AppTheme.bodySmall,
                  ),
                  const Spacer(),
                  if (event.eventDate != null) ...[
                    Icon(Icons.calendar_today,
                        size: 16, color: Colors.grey[600]),
                    const SizedBox(width: AppTheme.spacingXS),
                    Text(
                      '${event.eventDate!.day}/${event.eventDate!.month}/${event.eventDate!.year}',
                      style: AppTheme.bodySmall,
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(EventStatus status) {
    Color color;
    switch (status) {
      case EventStatus.active:
        color = Colors.green;
        break;
      case EventStatus.booked:
        color = Colors.blue;
        break;
      case EventStatus.inProgress:
        color = Colors.orange;
        break;
      case EventStatus.completed:
        color = Colors.purple;
        break;
      case EventStatus.cancelled:
        color = Colors.red;
        break;
      case EventStatus.draft:
        color = Colors.grey;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingS,
        vertical: AppTheme.spacingXS,
      ),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusS),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        status.toString().split('.').last.toUpperCase(),
        style: AppTheme.bodySmall.copyWith(
          color: color,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  String _getCategoryDisplayName(EventCategory category) {
    switch (category) {
      case EventCategory.wedding:
        return 'Wedding';
      case EventCategory.corporate:
        return 'Corporate';
      case EventCategory.birthday:
        return 'Birthday';
      case EventCategory.anniversary:
        return 'Anniversary';
      case EventCategory.engagement:
        return 'Engagement';
      case EventCategory.reception:
        return 'Reception';
      case EventCategory.conference:
        return 'Conference';
      case EventCategory.seminar:
        return 'Seminar';
      case EventCategory.workshop:
        return 'Workshop';
      case EventCategory.party:
        return 'Party';
      case EventCategory.festival:
        return 'Festival';
      case EventCategory.exhibition:
        return 'Exhibition';
      case EventCategory.launch:
        return 'Launch';
      case EventCategory.other:
        return 'Other';
    }
  }

  void _navigateToAddEvent({EventCategory? category}) {
    Navigator.of(context)
        .push(
      MaterialPageRoute(
        builder: (context) => AddEventScreen(initialCategory: category),
      ),
    )
        .then((result) {
      if (result == true) {
        // Refresh events list
        _loadEvents();
      }
    });
  }

  void _navigateToEventDetail(EventModel event) {
    Navigator.of(context)
        .push(
      MaterialPageRoute(
        builder: (context) => EventDetailScreen(event: event),
      ),
    )
        .then((result) {
      if (result == true) {
        // Refresh events list if event was deleted or updated
        _loadEvents();
      }
    });
  }
}
