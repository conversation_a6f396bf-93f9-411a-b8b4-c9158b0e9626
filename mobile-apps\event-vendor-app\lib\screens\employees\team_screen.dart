import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/vendor_auth_provider.dart';
import '../../config/app_theme.dart';
import '../../widgets/app_logo.dart';

class TeamScreen extends StatefulWidget {
  const TeamScreen({super.key});

  @override
  State<TeamScreen> createState() => _TeamScreenState();
}

class _TeamScreenState extends State<TeamScreen> {
  final List<TeamMember> _teamMembers = [];

  @override
  void initState() {
    super.initState();
    _loadSampleTeamMembers();
  }

  void _loadSampleTeamMembers() {
    // Load sample team members - in a real app, this would come from Firestore
    _teamMembers.addAll([
      TeamMember(
        id: '1',
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+91 98765 43210',
        role: TeamRole.manager,
        department: 'Operations',
        joinDate: DateTime.now().subtract(const Duration(days: 180)),
        status: EmployeeStatus.active,
        avatar: null,
        skills: ['Event Planning', 'Team Leadership', 'Client Relations'],
        salary: 45000,
      ),
      TeamMember(
        id: '2',
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+91 98765 43211',
        role: TeamRole.coordinator,
        department: 'Events',
        joinDate: DateTime.now().subtract(const Duration(days: 120)),
        status: EmployeeStatus.active,
        avatar: null,
        skills: ['Event Coordination', 'Vendor Management', 'Budget Planning'],
        salary: 35000,
      ),
      TeamMember(
        id: '3',
        name: 'Mike Wilson',
        email: '<EMAIL>',
        phone: '+91 98765 43212',
        role: TeamRole.technician,
        department: 'Technical',
        joinDate: DateTime.now().subtract(const Duration(days: 90)),
        status: EmployeeStatus.active,
        avatar: null,
        skills: [
          'Audio/Visual Setup',
          'Equipment Maintenance',
          'Technical Support'
        ],
        salary: 30000,
      ),
      TeamMember(
        id: '4',
        name: 'Emily Davis',
        email: '<EMAIL>',
        phone: '+91 98765 43213',
        role: TeamRole.assistant,
        department: 'Support',
        joinDate: DateTime.now().subtract(const Duration(days: 60)),
        status: EmployeeStatus.onLeave,
        avatar: null,
        skills: ['Customer Service', 'Data Entry', 'Administrative Support'],
        salary: 25000,
      ),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<VendorAuthProvider>(context);
    final vendor = authProvider.currentVendor;

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Transform.translate(
              offset: const Offset(-8, 0), // Move logo 8px to the left
              child: const AppLogo(
                size: AppLogoSizes.header,
                variant: LogoVariant.white,
                showBackground: false,
              ),
            ),
            const SizedBox(width: 4), // Reduced from 12 to 4
            const Text('Team Management'),
          ],
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.person_add),
            onPressed: () => _showAddMemberDialog(),
          ),
        ],
      ),
      body: Column(
        children: [
          // Team Stats
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey.shade50,
            child: Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Members',
                    '${_teamMembers.length}',
                    Icons.people,
                    AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Active',
                    '${_teamMembers.where((m) => m.status == EmployeeStatus.active).length}',
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'On Leave',
                    '${_teamMembers.where((m) => m.status == EmployeeStatus.onLeave).length}',
                    Icons.schedule,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ),

          // Team Members List
          Expanded(
            child: _teamMembers.isEmpty
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.people_outline,
                            size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text(
                          'No team members yet',
                          style: TextStyle(fontSize: 18, color: Colors.grey),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Add your first team member to get started',
                          style: TextStyle(color: Colors.grey),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _teamMembers.length,
                    itemBuilder: (context, index) {
                      final member = _teamMembers[index];
                      return _buildMemberCard(member);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMemberCard(TeamMember member) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: InkWell(
        onTap: () => _showMemberDetails(member),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Avatar
              CircleAvatar(
                radius: 30,
                backgroundColor: AppTheme.primaryColor,
                child: member.avatar != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(30),
                        child: Image.network(
                          member.avatar!,
                          width: 60,
                          height: 60,
                          fit: BoxFit.cover,
                        ),
                      )
                    : Text(
                        member.name.split(' ').map((n) => n[0]).take(2).join(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
              ),
              const SizedBox(width: 16),

              // Member Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            member.name,
                            style: AppTheme.headingSmall,
                          ),
                        ),
                        _buildStatusChip(member.status),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _getRoleDisplayName(member.role),
                      style: TextStyle(
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      member.department,
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(Icons.email,
                            size: 14, color: Colors.grey.shade600),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            member.email,
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 12,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(Icons.phone,
                            size: 14, color: Colors.grey.shade600),
                        const SizedBox(width: 4),
                        Text(
                          member.phone,
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Action Button
              IconButton(
                icon: const Icon(Icons.more_vert),
                onPressed: () => _showMemberActions(member),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(EmployeeStatus status) {
    Color color;
    String label;
    IconData icon;

    switch (status) {
      case EmployeeStatus.active:
        color = Colors.green;
        label = 'Active';
        icon = Icons.check_circle;
        break;
      case EmployeeStatus.onLeave:
        color = Colors.orange;
        label = 'On Leave';
        icon = Icons.schedule;
        break;
      case EmployeeStatus.inactive:
        color = Colors.red;
        label = 'Inactive';
        icon = Icons.cancel;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  String _getRoleDisplayName(TeamRole role) {
    switch (role) {
      case TeamRole.manager:
        return 'Manager';
      case TeamRole.coordinator:
        return 'Event Coordinator';
      case TeamRole.technician:
        return 'Technician';
      case TeamRole.assistant:
        return 'Assistant';
    }
  }

  void _showMemberDetails(TeamMember member) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(member.name),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow('Role', _getRoleDisplayName(member.role)),
              _buildDetailRow('Department', member.department),
              _buildDetailRow('Email', member.email),
              _buildDetailRow('Phone', member.phone),
              _buildDetailRow(
                  'Join Date', member.joinDate.toString().split(' ')[0]),
              _buildDetailRow('Salary', '₹${member.salary.toStringAsFixed(0)}'),
              const SizedBox(height: 16),
              const Text(
                'Skills:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: member.skills
                    .map((skill) => Chip(
                          label:
                              Text(skill, style: const TextStyle(fontSize: 12)),
                          materialTapTargetSize:
                              MaterialTapTargetSize.shrinkWrap,
                        ))
                    .toList(),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showEditMemberDialog(member);
            },
            child: const Text('Edit'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _showMemberActions(TeamMember member) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Edit Member'),
              onTap: () {
                Navigator.pop(context);
                _showEditMemberDialog(member);
              },
            ),
            ListTile(
              leading: const Icon(Icons.schedule),
              title: Text(member.status == EmployeeStatus.active
                  ? 'Mark On Leave'
                  : 'Mark Active'),
              onTap: () {
                setState(() {
                  member.status = member.status == EmployeeStatus.active
                      ? EmployeeStatus.onLeave
                      : EmployeeStatus.active;
                });
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('Remove Member',
                  style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                _confirmRemoveMember(member);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showAddMemberDialog() {
    final formKey = GlobalKey<FormState>();
    final nameController = TextEditingController();
    final emailController = TextEditingController();
    final phoneController = TextEditingController();
    final departmentController = TextEditingController();
    final salaryController = TextEditingController();
    final skillsController = TextEditingController();
    TeamRole selectedRole = TeamRole.assistant;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Team Member'),
        content: SizedBox(
          width: double.maxFinite,
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Name Field
                  TextFormField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'Full Name *',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter full name';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Email Field
                  TextFormField(
                    controller: emailController,
                    decoration: const InputDecoration(
                      labelText: 'Email Address *',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.emailAddress,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter email address';
                      }
                      if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                          .hasMatch(value)) {
                        return 'Please enter a valid email address';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Phone Field
                  TextFormField(
                    controller: phoneController,
                    decoration: const InputDecoration(
                      labelText: 'Phone Number *',
                      border: OutlineInputBorder(),
                      prefixText: '+91 ',
                    ),
                    keyboardType: TextInputType.phone,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter phone number';
                      }
                      if (!RegExp(r'^[0-9]{10}$')
                          .hasMatch(value.replaceAll(' ', ''))) {
                        return 'Please enter a valid 10-digit phone number';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Role Dropdown
                  DropdownButtonFormField<TeamRole>(
                    value: selectedRole,
                    decoration: const InputDecoration(
                      labelText: 'Role *',
                      border: OutlineInputBorder(),
                    ),
                    items: TeamRole.values.map((role) {
                      return DropdownMenuItem(
                        value: role,
                        child: Text(_getRoleDisplayName(role)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        selectedRole = value;
                      }
                    },
                  ),
                  const SizedBox(height: 16),

                  // Department Field
                  TextFormField(
                    controller: departmentController,
                    decoration: const InputDecoration(
                      labelText: 'Department *',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter department';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Salary Field
                  TextFormField(
                    controller: salaryController,
                    decoration: const InputDecoration(
                      labelText: 'Monthly Salary (₹) *',
                      border: OutlineInputBorder(),
                      prefixText: '₹ ',
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter salary';
                      }
                      final salary = double.tryParse(value);
                      if (salary == null || salary <= 0) {
                        return 'Please enter a valid salary amount';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Skills Field
                  TextFormField(
                    controller: skillsController,
                    decoration: const InputDecoration(
                      labelText: 'Skills (comma separated)',
                      border: OutlineInputBorder(),
                      hintText: 'e.g., Event Planning, Team Leadership',
                    ),
                    maxLines: 2,
                  ),
                ],
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (formKey.currentState!.validate()) {
                final newMember = TeamMember(
                  id: DateTime.now().millisecondsSinceEpoch.toString(),
                  name: nameController.text.trim(),
                  email: emailController.text.trim(),
                  phone: '+91 ${phoneController.text.trim()}',
                  role: selectedRole,
                  department: departmentController.text.trim(),
                  joinDate: DateTime.now(),
                  status: EmployeeStatus.active,
                  skills: skillsController.text.trim().isEmpty
                      ? []
                      : skillsController.text
                          .split(',')
                          .map((s) => s.trim())
                          .where((s) => s.isNotEmpty)
                          .toList(),
                  salary: double.parse(salaryController.text.trim()),
                );

                setState(() {
                  _teamMembers.add(newMember);
                });

                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content:
                        Text('${newMember.name} added to team successfully!'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            child: const Text('Add Member'),
          ),
        ],
      ),
    );
  }

  void _showEditMemberDialog(TeamMember member) {
    final formKey = GlobalKey<FormState>();
    final nameController = TextEditingController(text: member.name);
    final emailController = TextEditingController(text: member.email);
    final phoneController =
        TextEditingController(text: member.phone.replaceFirst('+91 ', ''));
    final departmentController = TextEditingController(text: member.department);
    final salaryController =
        TextEditingController(text: member.salary.toString());
    final skillsController =
        TextEditingController(text: member.skills.join(', '));
    TeamRole selectedRole = member.role;
    EmployeeStatus selectedStatus = member.status;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit ${member.name}'),
        content: SizedBox(
          width: double.maxFinite,
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Name Field
                  TextFormField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'Full Name *',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter full name';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Email Field
                  TextFormField(
                    controller: emailController,
                    decoration: const InputDecoration(
                      labelText: 'Email Address *',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.emailAddress,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter email address';
                      }
                      if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                          .hasMatch(value)) {
                        return 'Please enter a valid email address';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Phone Field
                  TextFormField(
                    controller: phoneController,
                    decoration: const InputDecoration(
                      labelText: 'Phone Number *',
                      border: OutlineInputBorder(),
                      prefixText: '+91 ',
                    ),
                    keyboardType: TextInputType.phone,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter phone number';
                      }
                      if (!RegExp(r'^[0-9]{10}$')
                          .hasMatch(value.replaceAll(' ', ''))) {
                        return 'Please enter a valid 10-digit phone number';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Role Dropdown
                  DropdownButtonFormField<TeamRole>(
                    value: selectedRole,
                    decoration: const InputDecoration(
                      labelText: 'Role *',
                      border: OutlineInputBorder(),
                    ),
                    items: TeamRole.values.map((role) {
                      return DropdownMenuItem(
                        value: role,
                        child: Text(_getRoleDisplayName(role)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        selectedRole = value;
                      }
                    },
                  ),
                  const SizedBox(height: 16),

                  // Status Dropdown
                  DropdownButtonFormField<EmployeeStatus>(
                    value: selectedStatus,
                    decoration: const InputDecoration(
                      labelText: 'Status *',
                      border: OutlineInputBorder(),
                    ),
                    items: EmployeeStatus.values.map((status) {
                      return DropdownMenuItem(
                        value: status,
                        child: Text(_getStatusDisplayName(status)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        selectedStatus = value;
                      }
                    },
                  ),
                  const SizedBox(height: 16),

                  // Department Field
                  TextFormField(
                    controller: departmentController,
                    decoration: const InputDecoration(
                      labelText: 'Department *',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter department';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Salary Field
                  TextFormField(
                    controller: salaryController,
                    decoration: const InputDecoration(
                      labelText: 'Monthly Salary (₹) *',
                      border: OutlineInputBorder(),
                      prefixText: '₹ ',
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter salary';
                      }
                      final salary = double.tryParse(value);
                      if (salary == null || salary <= 0) {
                        return 'Please enter a valid salary amount';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Skills Field
                  TextFormField(
                    controller: skillsController,
                    decoration: const InputDecoration(
                      labelText: 'Skills (comma separated)',
                      border: OutlineInputBorder(),
                      hintText: 'e.g., Event Planning, Team Leadership',
                    ),
                    maxLines: 2,
                  ),
                ],
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (formKey.currentState!.validate()) {
                final updatedMember = TeamMember(
                  id: member.id,
                  name: nameController.text.trim(),
                  email: emailController.text.trim(),
                  phone: '+91 ${phoneController.text.trim()}',
                  role: selectedRole,
                  department: departmentController.text.trim(),
                  joinDate: member.joinDate,
                  status: selectedStatus,
                  skills: skillsController.text.trim().isEmpty
                      ? []
                      : skillsController.text
                          .split(',')
                          .map((s) => s.trim())
                          .where((s) => s.isNotEmpty)
                          .toList(),
                  salary: double.parse(salaryController.text.trim()),
                );

                setState(() {
                  final index =
                      _teamMembers.indexWhere((m) => m.id == member.id);
                  if (index != -1) {
                    _teamMembers[index] = updatedMember;
                  }
                });

                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content:
                        Text('${updatedMember.name} updated successfully!'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  String _getStatusDisplayName(EmployeeStatus status) {
    switch (status) {
      case EmployeeStatus.active:
        return 'Active';
      case EmployeeStatus.onLeave:
        return 'On Leave';
      case EmployeeStatus.inactive:
        return 'Inactive';
    }
  }

  void _confirmRemoveMember(TeamMember member) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove Team Member'),
        content: Text(
            'Are you sure you want to remove ${member.name} from your team?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _teamMembers.remove(member);
              });
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('${member.name} removed from team')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Remove', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}

// Team Models
enum TeamRole { manager, coordinator, technician, assistant }

enum EmployeeStatus { active, onLeave, inactive }

class TeamMember {
  final String id;
  final String name;
  final String email;
  final String phone;
  final TeamRole role;
  final String department;
  final DateTime joinDate;
  EmployeeStatus status;
  final String? avatar;
  final List<String> skills;
  final double salary;

  TeamMember({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.role,
    required this.department,
    required this.joinDate,
    required this.status,
    this.avatar,
    required this.skills,
    required this.salary,
  });
}
