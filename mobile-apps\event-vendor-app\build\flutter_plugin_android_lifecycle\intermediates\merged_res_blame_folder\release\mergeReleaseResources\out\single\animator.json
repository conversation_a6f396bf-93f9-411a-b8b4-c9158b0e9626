[{"merged": "io.flutter.plugins.flutter_plugin_android_lifecycle-release-25:/animator/fragment_close_enter.xml", "source": "io.flutter.plugins.flutter_plugin_android_lifecycle-fragment-1.7.1-1:/animator/fragment_close_enter.xml"}, {"merged": "io.flutter.plugins.flutter_plugin_android_lifecycle-release-25:/animator/fragment_fade_exit.xml", "source": "io.flutter.plugins.flutter_plugin_android_lifecycle-fragment-1.7.1-1:/animator/fragment_fade_exit.xml"}, {"merged": "io.flutter.plugins.flutter_plugin_android_lifecycle-release-25:/animator/fragment_open_enter.xml", "source": "io.flutter.plugins.flutter_plugin_android_lifecycle-fragment-1.7.1-1:/animator/fragment_open_enter.xml"}, {"merged": "io.flutter.plugins.flutter_plugin_android_lifecycle-release-25:/animator/fragment_open_exit.xml", "source": "io.flutter.plugins.flutter_plugin_android_lifecycle-fragment-1.7.1-1:/animator/fragment_open_exit.xml"}, {"merged": "io.flutter.plugins.flutter_plugin_android_lifecycle-release-25:/animator/fragment_fade_enter.xml", "source": "io.flutter.plugins.flutter_plugin_android_lifecycle-fragment-1.7.1-1:/animator/fragment_fade_enter.xml"}, {"merged": "io.flutter.plugins.flutter_plugin_android_lifecycle-release-25:/animator/fragment_close_exit.xml", "source": "io.flutter.plugins.flutter_plugin_android_lifecycle-fragment-1.7.1-1:/animator/fragment_close_exit.xml"}]