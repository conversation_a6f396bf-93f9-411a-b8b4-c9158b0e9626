[{"merged": "io.flutter.plugins.firebase.messaging.firebase_messaging-release-31:/layout-v21/notification_action_tombstone.xml", "source": "io.flutter.plugins.firebase.messaging.firebase_messaging-core-1.13.1-19:/layout-v21/notification_action_tombstone.xml"}, {"merged": "io.flutter.plugins.firebase.messaging.firebase_messaging-release-31:/layout-v21/notification_template_custom_big.xml", "source": "io.flutter.plugins.firebase.messaging.firebase_messaging-core-1.13.1-19:/layout-v21/notification_template_custom_big.xml"}, {"merged": "io.flutter.plugins.firebase.messaging.firebase_messaging-release-31:/layout-v21/notification_action.xml", "source": "io.flutter.plugins.firebase.messaging.firebase_messaging-core-1.13.1-19:/layout-v21/notification_action.xml"}, {"merged": "io.flutter.plugins.firebase.messaging.firebase_messaging-release-31:/layout-v21/notification_template_icon_group.xml", "source": "io.flutter.plugins.firebase.messaging.firebase_messaging-core-1.13.1-19:/layout-v21/notification_template_icon_group.xml"}]