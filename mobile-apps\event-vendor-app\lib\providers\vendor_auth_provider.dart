import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/vendor_model.dart';
import '../services/vendor_auth_service.dart';
import '../services/aadhar_verification_service.dart';

class VendorAuthProvider extends ChangeNotifier {
  final VendorAuthService _authService = VendorAuthService();
  final AadharVerificationService _aadharService = AadharVerificationService();

  VendorModel? _currentVendor;
  bool _isLoading = false;
  String? _errorMessage;
  bool _isEmailVerified = false;

  // Aadhar verification state
  bool _isAadharLoading = false;
  String? _aadharOtpReferenceId;
  String? _aadharNumber;

  // Getters
  VendorModel? get currentVendor => _currentVendor;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _currentVendor != null;
  bool get isEmailVerified => _isEmailVerified;
  bool get isApproved => _currentVendor?.isApproved ?? false;

  // Aadhar verification getters
  bool get isAadharLoading => _isAadharLoading;
  String? get aadharOtpReferenceId => _aadharOtpReferenceId;
  String? get aadharNumber => _aadharNumber;

  // Initialize auth state
  void initialize() {
    _authService.authStateChanges.listen((User? user) async {
      if (user != null) {
        _isEmailVerified = user.emailVerified;
        await _loadCurrentVendor();
      } else {
        _currentVendor = null;
        _isEmailVerified = false;
        notifyListeners();
      }
    });
  }

  // Load current vendor data
  Future<void> _loadCurrentVendor() async {
    try {
      _currentVendor = await _authService.getCurrentVendorModel();
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('Error loading current vendor: $e');
      }
    }
  }

  // Sign in with email and password
  Future<bool> signInWithEmailPassword(String email, String password) async {
    _setLoading(true);
    _clearError();

    try {
      final result =
          await _authService.signInWithEmailPassword(email, password);

      if (result.isSuccess) {
        _currentVendor = result.vendor;
        _setLoading(false);
        return true;
      } else {
        _setError(result.message ?? 'Sign in failed');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('An unexpected error occurred');
      _setLoading(false);
      return false;
    }
  }

  // Sign up with email and password
  Future<bool> signUpWithEmailPassword(
    String email,
    String password,
    String businessName,
    String firstName,
    String lastName,
  ) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _authService.signUpWithEmailPassword(
        email,
        password,
        businessName,
        firstName,
        lastName,
      );

      if (result.isSuccess) {
        _currentVendor = result.vendor;
        _setLoading(false);
        return true;
      } else {
        _setError(result.message ?? 'Sign up failed');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('An unexpected error occurred');
      _setLoading(false);
      return false;
    }
  }

  // Sign in with Google
  Future<bool> signInWithGoogle() async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _authService.signInWithGoogle();

      if (result.isSuccess) {
        _currentVendor = result.vendor;
        _setLoading(false);
        return true;
      } else {
        _setError(result.message ?? 'Google sign in failed');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Google sign in failed');
      _setLoading(false);
      return false;
    }
  }

  // Send phone OTP
  Future<bool> sendPhoneOTP(String phoneNumber) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _authService.sendPhoneOTP(phoneNumber);

      if (result.isSuccess) {
        _setLoading(false);
        return true;
      } else {
        _setError(result.message ?? 'Failed to send OTP');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Failed to send OTP');
      _setLoading(false);
      return false;
    }
  }

  // Verify phone OTP
  Future<bool> verifyPhoneOTP(String phoneNumber, String otp) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _authService.verifyPhoneOTP(phoneNumber, otp);

      if (result.isSuccess) {
        _currentVendor = result.vendor;
        _setLoading(false);
        return true;
      } else {
        _setError(result.message ?? 'Invalid OTP');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('OTP verification failed');
      _setLoading(false);
      return false;
    }
  }

  // Update vendor profile
  Future<bool> updateVendorProfile(VendorModel vendorModel) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _authService.updateVendorProfile(vendorModel);

      if (result.isSuccess) {
        _currentVendor = result.vendor;
        _setLoading(false);
        return true;
      } else {
        _setError(result.message ?? 'Failed to update profile');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Failed to update profile');
      _setLoading(false);
      return false;
    }
  }

  // Change password
  Future<bool> changePassword(
      String currentPassword, String newPassword) async {
    _setLoading(true);
    _clearError();

    try {
      final result =
          await _authService.changePassword(currentPassword, newPassword);

      if (result.isSuccess) {
        _setLoading(false);
        return true;
      } else {
        _setError(result.message ?? 'Failed to change password');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Failed to change password');
      _setLoading(false);
      return false;
    }
  }

  // Sign out
  Future<void> signOut() async {
    _setLoading(true);

    try {
      await _authService.signOut();
      _currentVendor = null;
      _isEmailVerified = false;
      _clearError();
    } catch (e) {
      _setError('Failed to sign out');
    } finally {
      _setLoading(false);
    }
  }

  // Refresh current vendor data
  Future<void> refreshCurrentVendor() async {
    try {
      final refreshedVendor = await _authService.getCurrentVendor();
      if (refreshedVendor != null) {
        _currentVendor = refreshedVendor;
        notifyListeners();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error refreshing vendor data: $e');
      }
    }
  }

  // Check if vendor needs to complete profile
  bool get needsProfileCompletion {
    if (_currentVendor == null) return false;

    return _currentVendor!.profile.businessName.isEmpty ||
        _currentVendor!.profile.firstName.isEmpty ||
        _currentVendor!.profile.lastName.isEmpty ||
        !_currentVendor!.isVerified;
  }

  // Check if vendor needs Aadhaar verification
  bool get needsAadhaarVerification {
    if (_currentVendor == null) return false;
    return !_currentVendor!.aadhaarVerified;
  }

  // Check if vendor needs approval
  bool get needsApproval {
    if (_currentVendor == null) return false;
    return !_currentVendor!.isApproved;
  }

  // Refresh current vendor data
  Future<void> refreshVendor() async {
    if (_authService.currentUser != null) {
      await _loadCurrentVendor();
    }
  }

  // Aadhar verification methods
  Future<Map<String, dynamic>> generateAadharOtp(String aadharNumber) async {
    _isAadharLoading = true;
    _clearError();
    notifyListeners();

    try {
      final result = await _aadharService.generateAadharOtp(aadharNumber);

      if (result['success']) {
        _aadharOtpReferenceId = result['data']['referenceId'];
        _aadharNumber = aadharNumber;
        _isAadharLoading = false;
        notifyListeners();
        return result;
      } else {
        _setError(result['error'] ?? 'Failed to generate OTP');
        _isAadharLoading = false;
        notifyListeners();
        return result;
      }
    } catch (e) {
      _setError('Network error: $e');
      _isAadharLoading = false;
      notifyListeners();
      return {
        'success': false,
        'error': 'Network error: $e',
        'data': null,
      };
    }
  }

  Future<Map<String, dynamic>> verifyAadharOtp(String otp) async {
    if (_aadharOtpReferenceId == null) {
      return {
        'success': false,
        'error': 'No OTP reference found. Please generate OTP first.',
        'data': null,
      };
    }

    _isAadharLoading = true;
    _clearError();
    notifyListeners();

    try {
      final result =
          await _aadharService.verifyAadharOtp(_aadharOtpReferenceId!, otp);

      if (result['success'] && result['data']['isValid']) {
        // Update vendor's Aadhar verification status
        await _authService.updateAadharVerification(
          _aadharNumber!,
          result['data'],
        );

        // Refresh vendor data
        await _loadCurrentVendor();

        _isAadharLoading = false;
        notifyListeners();
        return result;
      } else {
        _setError(result['error'] ?? 'OTP verification failed');
        _isAadharLoading = false;
        notifyListeners();
        return result;
      }
    } catch (e) {
      _setError('Network error: $e');
      _isAadharLoading = false;
      notifyListeners();
      return {
        'success': false,
        'error': 'Network error: $e',
        'data': null,
      };
    }
  }

  // Clear Aadhar verification state
  void clearAadharVerification() {
    _aadharOtpReferenceId = null;
    _aadharNumber = null;
    _isAadharLoading = false;
    notifyListeners();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Clear error message
  void clearError() {
    _clearError();
  }
}
