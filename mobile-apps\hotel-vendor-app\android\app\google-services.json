2{
  "project_info": {
    "project_number": "408299360705",
    "project_id": "linkinblink-f544a",
    "storage_bucket": "linkinblink-f544a.firebasestorage.app"
  },
  "client": [
    {
      "client_info": {
        "mobilesdk_app_id": "1:408299360705:android:553b6964daacc3dfdd5f40",
        "android_client_info": {
          "package_name": "com.eventlycustomerapp"
        }
      },
      "oauth_client": [
        {
          "client_id": "408299360705-sj2fdnij7tpi1pgkcp6qi8dtdvi2a9t4.apps.googleusercontent.com",
          "client_type": 3
        }
      ],
      "api_key": [
        {
          "current_key": "AIzaSyBpMQS5vgW9f39NNmcN-XFHALaN1_ar5bg"
        }
      ],
      "services": {
        "appinvite_service": {
          "other_platform_oauth_client": [
            {
              "client_id": "408299360705-sj2fdnij7tpi1pgkcp6qi8dtdvi2a9t4.apps.googleusercontent.com",
              "client_type": 3
            },
            {
              "client_id": "408299360705-7k9o0nlses17a0jf382g32b9746lpe05.apps.googleusercontent.com",
              "client_type": 2,
              "ios_info": {
                "bundle_id": "magicmate"
              }
            }
          ]
        }
      }
    },
    {
      "client_info": {
        "mobilesdk_app_id": "1:408299360705:android:b719d1b14efe458bdd5f40",
        "android_client_info": {
          "package_name": "com.example.customer_app"
        }
      },
      "oauth_client": [
        {
          "client_id": "408299360705-sj2fdnij7tpi1pgkcp6qi8dtdvi2a9t4.apps.googleusercontent.com",
          "client_type": 3
        }
      ],
      "api_key": [
        {
          "current_key": "AIzaSyBpMQS5vgW9f39NNmcN-XFHALaN1_ar5bg"
        }
      ],
      "services": {
        "appinvite_service": {
          "other_platform_oauth_client": [
            {
              "client_id": "408299360705-sj2fdnij7tpi1pgkcp6qi8dtdvi2a9t4.apps.googleusercontent.com",
              "client_type": 3
            },
            {
              "client_id": "408299360705-7k9o0nlses17a0jf382g32b9746lpe05.apps.googleusercontent.com",
              "client_type": 2,
              "ios_info": {
                "bundle_id": "magicmate"
              }
            }
          ]
        }
      }
    },
    {
      "client_info": {
        "mobilesdk_app_id": "1:408299360705:android:a5d72f19139d86c4dd5f40",
        "android_client_info": {
          "package_name": "com.example.event_vendor_app"
        }
      },
      "oauth_client": [
        {
          "client_id": "408299360705-i1eo3vod2a3lr0a5islhtkuuoefqhg2b.apps.googleusercontent.com",
          "client_type": 1,
          "android_info": {
            "package_name": "com.example.event_vendor_app",
            "certificate_hash": "7edde07674ff388a918a933f1e28b051890e6a90"
          }
        },
        {
          "client_id": "408299360705-sj2fdnij7tpi1pgkcp6qi8dtdvi2a9t4.apps.googleusercontent.com",
          "client_type": 3
        }
      ],
      "api_key": [
        {
          "current_key": "AIzaSyBpMQS5vgW9f39NNmcN-XFHALaN1_ar5bg"
        }
      ],
      "services": {
        "appinvite_service": {
          "other_platform_oauth_client": [
            {
              "client_id": "408299360705-sj2fdnij7tpi1pgkcp6qi8dtdvi2a9t4.apps.googleusercontent.com",
              "client_type": 3
            },
            {
              "client_id": "408299360705-7k9o0nlses17a0jf382g32b9746lpe05.apps.googleusercontent.com",
              "client_type": 2,
              "ios_info": {
                "bundle_id": "magicmate"
              }
            }
          ]
        }
      }
    },
    {
      "client_info": {
        "mobilesdk_app_id": "1:408299360705:android:c63f7f888e3297cddd5f40",
        "android_client_info": {
          "package_name": "com.hotelvendor.management"
        }
      },
      "oauth_client": [
        {
          "client_id": "408299360705-tvf8lbde3fhf1r3h6eq90gqvkhfjg7pc.apps.googleusercontent.com",
          "client_type": 1,
          "android_info": {
            "package_name": "com.hotelvendor.management",
            "certificate_hash": "7edde07674ff388a918a933f1e28b051890e6a90"
          }
        },
        {
          "client_id": "408299360705-sj2fdnij7tpi1pgkcp6qi8dtdvi2a9t4.apps.googleusercontent.com",
          "client_type": 3
        }
      ],
      "api_key": [
        {
          "current_key": "AIzaSyBpMQS5vgW9f39NNmcN-XFHALaN1_ar5bg"
        }
      ],
      "services": {
        "appinvite_service": {
          "other_platform_oauth_client": [
            {
              "client_id": "408299360705-sj2fdnij7tpi1pgkcp6qi8dtdvi2a9t4.apps.googleusercontent.com",
              "client_type": 3
            },
            {
              "client_id": "408299360705-7k9o0nlses17a0jf382g32b9746lpe05.apps.googleusercontent.com",
              "client_type": 2,
              "ios_info": {
                "bundle_id": "magicmate"
              }
            }
          ]
        }
      }
    }
  ],
  "configuration_version": "1"
}