#Tue Aug 05 17:27:49 IST 2025
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-anydpi-v21/ic_call_answer.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21\\ic_call_answer.xml
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-anydpi-v21/ic_call_answer_low.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21\\ic_call_answer_low.xml
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-anydpi-v21/ic_call_answer_video.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21\\ic_call_answer_video.xml
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-anydpi-v21/ic_call_answer_video_low.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21\\ic_call_answer_video_low.xml
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-anydpi-v21/ic_call_decline.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21\\ic_call_decline.xml
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-anydpi-v21/ic_call_decline_low.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21\\ic_call_decline_low.xml
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-hdpi-v4/ic_call_answer.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\ic_call_answer.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-hdpi-v4/ic_call_answer_low.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\ic_call_answer_low.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-hdpi-v4/ic_call_answer_video.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\ic_call_answer_video.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-hdpi-v4/ic_call_answer_video_low.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\ic_call_answer_video_low.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-hdpi-v4/ic_call_decline.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\ic_call_decline.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-hdpi-v4/ic_call_decline_low.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\ic_call_decline_low.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-hdpi-v4/notification_bg_low_normal.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\notification_bg_low_normal.9.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-hdpi-v4/notification_bg_low_pressed.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\notification_bg_low_pressed.9.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-hdpi-v4/notification_bg_normal.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\notification_bg_normal.9.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-hdpi-v4/notification_bg_normal_pressed.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\notification_bg_normal_pressed.9.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-hdpi-v4/notification_oversize_large_icon_bg.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\notification_oversize_large_icon_bg.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-hdpi-v4/notify_panel_notification_icon_bg.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\notify_panel_notification_icon_bg.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-ldpi-v4/ic_call_answer.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4\\ic_call_answer.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-ldpi-v4/ic_call_answer_low.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4\\ic_call_answer_low.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-ldpi-v4/ic_call_answer_video.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4\\ic_call_answer_video.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-ldpi-v4/ic_call_answer_video_low.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4\\ic_call_answer_video_low.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-ldpi-v4/ic_call_decline.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4\\ic_call_decline.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-ldpi-v4/ic_call_decline_low.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4\\ic_call_decline_low.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-mdpi-v4/ic_call_answer.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\ic_call_answer.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-mdpi-v4/ic_call_answer_low.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\ic_call_answer_low.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-mdpi-v4/ic_call_answer_video.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\ic_call_answer_video.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-mdpi-v4/ic_call_answer_video_low.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\ic_call_answer_video_low.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-mdpi-v4/ic_call_decline.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\ic_call_decline.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-mdpi-v4/ic_call_decline_low.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\ic_call_decline_low.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-mdpi-v4/notification_bg_low_normal.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\notification_bg_low_normal.9.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-mdpi-v4/notification_bg_low_pressed.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\notification_bg_low_pressed.9.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-mdpi-v4/notification_bg_normal.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\notification_bg_normal.9.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-mdpi-v4/notification_bg_normal_pressed.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\notification_bg_normal_pressed.9.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-mdpi-v4/notify_panel_notification_icon_bg.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\notify_panel_notification_icon_bg.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-v21/notification_action_background.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21\\notification_action_background.xml
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-xhdpi-v4/ic_call_answer.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\ic_call_answer.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-xhdpi-v4/ic_call_answer_low.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\ic_call_answer_low.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-xhdpi-v4/ic_call_answer_video.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\ic_call_answer_video.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-xhdpi-v4/ic_call_answer_video_low.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\ic_call_answer_video_low.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-xhdpi-v4/ic_call_decline.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\ic_call_decline.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-xhdpi-v4/ic_call_decline_low.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\ic_call_decline_low.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-xhdpi-v4/notification_bg_low_normal.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\notification_bg_low_normal.9.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-xhdpi-v4/notification_bg_low_pressed.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\notification_bg_low_pressed.9.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-xhdpi-v4/notification_bg_normal.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\notification_bg_normal.9.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-xhdpi-v4/notification_bg_normal_pressed.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\notification_bg_normal_pressed.9.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-xhdpi-v4/notify_panel_notification_icon_bg.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\notify_panel_notification_icon_bg.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-xxhdpi-v4/ic_call_answer.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\ic_call_answer.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-xxhdpi-v4/ic_call_answer_low.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\ic_call_answer_low.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-xxhdpi-v4/ic_call_answer_video.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\ic_call_answer_video.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-xxhdpi-v4/ic_call_answer_video_low.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\ic_call_answer_video_low.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-xxhdpi-v4/ic_call_decline.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\ic_call_decline.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-xxhdpi-v4/ic_call_decline_low.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\ic_call_decline_low.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-xxxhdpi-v4/ic_call_answer.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\ic_call_answer.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-xxxhdpi-v4/ic_call_answer_low.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\ic_call_answer_low.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-xxxhdpi-v4/ic_call_answer_video.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\ic_call_answer_video.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-xxxhdpi-v4/ic_call_answer_video_low.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\ic_call_answer_video_low.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-xxxhdpi-v4/ic_call_decline.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\ic_call_decline.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable-xxxhdpi-v4/ic_call_decline_low.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\ic_call_decline_low.png
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable/notification_bg.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\notification_bg.xml
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable/notification_bg_low.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\notification_bg_low.xml
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable/notification_icon_background.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\notification_icon_background.xml
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/drawable/notification_tile_bg.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\notification_tile_bg.xml
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/layout-v21/notification_action.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v21\\notification_action.xml
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/layout-v21/notification_action_tombstone.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v21\\notification_action_tombstone.xml
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/layout-v21/notification_template_custom_big.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v21\\notification_template_custom_big.xml
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/layout-v21/notification_template_icon_group.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v21\\notification_template_icon_group.xml
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/layout/custom_dialog.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\custom_dialog.xml
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/layout/ime_base_split_test_activity.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\ime_base_split_test_activity.xml
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/layout/ime_secondary_split_test_activity.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\ime_secondary_split_test_activity.xml
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/layout/notification_template_part_chronometer.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\notification_template_part_chronometer.xml
io.flutter.plugins.firebase.storage.firebase_storage-core-1.13.1-17\:/layout/notification_template_part_time.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\notification_template_part_time.xml
io.flutter.plugins.firebase.storage.firebase_storage-fragment-1.7.1-1\:/anim-v21/fragment_fast_out_extra_slow_in.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21\\fragment_fast_out_extra_slow_in.xml
io.flutter.plugins.firebase.storage.firebase_storage-fragment-1.7.1-1\:/animator/fragment_close_enter.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\fragment_close_enter.xml
io.flutter.plugins.firebase.storage.firebase_storage-fragment-1.7.1-1\:/animator/fragment_close_exit.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\fragment_close_exit.xml
io.flutter.plugins.firebase.storage.firebase_storage-fragment-1.7.1-1\:/animator/fragment_fade_enter.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\fragment_fade_enter.xml
io.flutter.plugins.firebase.storage.firebase_storage-fragment-1.7.1-1\:/animator/fragment_fade_exit.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\fragment_fade_exit.xml
io.flutter.plugins.firebase.storage.firebase_storage-fragment-1.7.1-1\:/animator/fragment_open_enter.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\fragment_open_enter.xml
io.flutter.plugins.firebase.storage.firebase_storage-fragment-1.7.1-1\:/animator/fragment_open_exit.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\fragment_open_exit.xml
io.flutter.plugins.firebase.storage.firebase_storage-jetified-firebase-common-21.0.0-5\:/raw/firebase_common_keep.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\firebase_common_keep.xml
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/color/common_google_signin_btn_text_dark.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\common_google_signin_btn_text_dark.xml
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/color/common_google_signin_btn_text_light.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\common_google_signin_btn_text_light.xml
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/color/common_google_signin_btn_tint.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\common_google_signin_btn_tint.xml
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-hdpi-v4/common_full_open_on_phone.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\common_full_open_on_phone.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-hdpi-v4/common_google_signin_btn_icon_dark_normal_background.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\common_google_signin_btn_icon_dark_normal_background.9.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-hdpi-v4/common_google_signin_btn_icon_light_normal_background.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\common_google_signin_btn_icon_light_normal_background.9.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-hdpi-v4/common_google_signin_btn_text_dark_normal_background.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\common_google_signin_btn_text_dark_normal_background.9.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-hdpi-v4/common_google_signin_btn_text_light_normal_background.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\common_google_signin_btn_text_light_normal_background.9.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-hdpi-v4/googleg_disabled_color_18.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\googleg_disabled_color_18.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-hdpi-v4/googleg_standard_color_18.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\googleg_standard_color_18.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-mdpi-v4/common_google_signin_btn_icon_dark_normal_background.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\common_google_signin_btn_icon_dark_normal_background.9.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-mdpi-v4/common_google_signin_btn_icon_light_normal_background.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\common_google_signin_btn_icon_light_normal_background.9.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-mdpi-v4/common_google_signin_btn_text_dark_normal_background.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\common_google_signin_btn_text_dark_normal_background.9.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-mdpi-v4/common_google_signin_btn_text_light_normal_background.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\common_google_signin_btn_text_light_normal_background.9.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-mdpi-v4/googleg_disabled_color_18.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\googleg_disabled_color_18.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-mdpi-v4/googleg_standard_color_18.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\googleg_standard_color_18.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-xhdpi-v4/common_full_open_on_phone.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\common_full_open_on_phone.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-xhdpi-v4/common_google_signin_btn_icon_dark_normal_background.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\common_google_signin_btn_icon_dark_normal_background.9.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-xhdpi-v4/common_google_signin_btn_icon_light_normal_background.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\common_google_signin_btn_icon_light_normal_background.9.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-xhdpi-v4/common_google_signin_btn_text_dark_normal_background.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\common_google_signin_btn_text_dark_normal_background.9.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-xhdpi-v4/common_google_signin_btn_text_light_normal_background.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\common_google_signin_btn_text_light_normal_background.9.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-xhdpi-v4/googleg_disabled_color_18.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\googleg_disabled_color_18.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-xhdpi-v4/googleg_standard_color_18.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\googleg_standard_color_18.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-xxhdpi-v4/common_google_signin_btn_icon_dark_normal_background.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\common_google_signin_btn_icon_dark_normal_background.9.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-xxhdpi-v4/common_google_signin_btn_icon_light_normal_background.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\common_google_signin_btn_icon_light_normal_background.9.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-xxhdpi-v4/common_google_signin_btn_text_dark_normal_background.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\common_google_signin_btn_text_dark_normal_background.9.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-xxhdpi-v4/common_google_signin_btn_text_light_normal_background.9.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\common_google_signin_btn_text_light_normal_background.9.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-xxhdpi-v4/googleg_disabled_color_18.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\googleg_disabled_color_18.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable-xxhdpi-v4/googleg_standard_color_18.png=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\googleg_standard_color_18.png
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable/common_google_signin_btn_icon_dark.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\common_google_signin_btn_icon_dark.xml
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable/common_google_signin_btn_icon_dark_focused.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\common_google_signin_btn_icon_dark_focused.xml
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable/common_google_signin_btn_icon_dark_normal.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\common_google_signin_btn_icon_dark_normal.xml
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable/common_google_signin_btn_icon_disabled.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\common_google_signin_btn_icon_disabled.xml
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable/common_google_signin_btn_icon_light.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\common_google_signin_btn_icon_light.xml
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable/common_google_signin_btn_icon_light_focused.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\common_google_signin_btn_icon_light_focused.xml
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable/common_google_signin_btn_icon_light_normal.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\common_google_signin_btn_icon_light_normal.xml
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable/common_google_signin_btn_text_dark.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\common_google_signin_btn_text_dark.xml
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable/common_google_signin_btn_text_dark_focused.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\common_google_signin_btn_text_dark_focused.xml
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable/common_google_signin_btn_text_dark_normal.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\common_google_signin_btn_text_dark_normal.xml
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable/common_google_signin_btn_text_disabled.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\common_google_signin_btn_text_disabled.xml
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable/common_google_signin_btn_text_light.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\common_google_signin_btn_text_light.xml
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable/common_google_signin_btn_text_light_focused.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\common_google_signin_btn_text_light_focused.xml
io.flutter.plugins.firebase.storage.firebase_storage-jetified-play-services-base-18.1.0-20\:/drawable/common_google_signin_btn_text_light_normal.xml=E\:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\firebase_storage\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\common_google_signin_btn_text_light_normal.xml
