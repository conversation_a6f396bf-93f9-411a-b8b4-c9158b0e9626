<variant
    name="release"
    package="com.example.event_vendor_app"
    minSdkVersion="23"
    targetSdkVersion="35"
    mergedManifest="E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\intermediates\merged_manifest\release\processReleaseMainManifest\AndroidManifest.xml"
    proguardFiles="E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.7.0;C:\Users\<USER>\dev\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro;proguard-rules.pro;E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.7.0;proguard-rules.pro"
    partialResultsDir="E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.9\transforms\f1001a85bd7f62b3542d6b75ae7e0b90\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <manifestPlaceholders>
    <placeholder
        name="applicationName"
        value="android.app.Application" />
  </manifestPlaceholders>
  <artifact
      classOutputs="E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\intermediates\javac\release\compileReleaseJavaWithJavac\classes;E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\tmp\kotlin-classes\release;E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\kotlinToolingMetadata;E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\processReleaseResources\R.jar"
      type="MAIN"
      applicationId="com.example.event_vendor_app"
      generatedSourceFolders="E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\generated\ap_generated_sources\release\out"
      generatedResourceFolders="E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.9\transforms\f1001a85bd7f62b3542d6b75ae7e0b90\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
